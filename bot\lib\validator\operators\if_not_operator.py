from typing import Callable

from lib.validator.class_operator import ClassOperator


class IfNotOperator(ClassOperator):
    def __init__(self, eval_condition: bool | Callable[[object], bool]):
        super().__init__(eval_condition)

    async def validate(self) -> bool:
        eval_condition = self.eval_condition()
        self.is_valid = False if eval_condition else True
        return self.is_valid
