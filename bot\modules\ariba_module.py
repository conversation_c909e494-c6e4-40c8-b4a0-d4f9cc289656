import re

from bs4 import BeautifulSoup
from requests import Response

from dtos.automation_dto import AribaResponseDto
from lib.ariba_session import AribaSession
from lib.ariba_tools import (
    ADMIN_PAGE_AWPX,
    BASE_PLATFORM_URL,
    CONSULTED_USER_PAGE_AWPX,
    USER_STATE_DIV_CLASS,
    USERS_FORM_AWPX,
    AribaResponse,
    AribaUserState,
    extract_422_input_name,
    extract_admin_tab_element_id,
    extract_admin_users_link_element_id,
    extract_admin_users_options_element_id,
    extract_aw_response_id,
    extract_awssk_param,
    extract_groups_tab_id,
    extract_logout_button_id,
    extract_search_button_id,
    extract_user_groups,
    extract_user_selection_form,
)
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.task_queue_manager import manager
from lib.tools import get_form_data, get_html_soup, retry


class AribaModule(BaseModule):
    """Provide a function to consult the data of
    a user on Ariba.
    """

    def __init__(self, ariba_session: AribaSession) -> None:
        self.ariba_session = ariba_session
        self.awssk = ''
        self.awr = ''
        self.logout_button_id = ''

    def _authenticate(self) -> Response:
        """Perform the authentication.

        Returns
        -------
        Response
            Response of the SAMLResponse service
            (last login response).

        """
        self.session = self.ariba_session.authenticate()
        return self.ariba_session.get_login_response()

    def _update_content_state(self, awr: str, awpx: str) -> Response:
        """Update the state of the content.

        Parameters
        ----------
        awr : str
            The awr param value returned by
            the previous request.
        awpx : str
            The awpx param value referring to the destination page.

        """
        params = {
            'awr': awr,
            'awssk': self.awssk,
            'awsn': 'awpxupdate',
            'awip': '1',
            'awpx': awpx,
        }
        return self.session.get(BASE_PLATFORM_URL, params=params)

    def _refresh_content(
        self,
        awpx: str,
        error_message: str | None = None,
    ) -> AribaResponse:
        """Refresh the content as required.

        Parameters
        ----------
        awpx : str
            The awpx param value referring to the destination page.
        error_message : str | None, optional
            Error message if an error raised, by default None.

        Returns
        -------
        AribaResponse
            Response of the refreshed content.

        Raises
        ------
        AutomationError
            If the content could not be refreshed.

        """
        params = {'awh': 'r', 'awssk': self.awssk, 'realm': 'sura'}
        refresh_response = self.session.get(BASE_PLATFORM_URL, params=params)
        if not refresh_response.ok:
            if not error_message:
                error_message = 'No se pudo actualizar el contenido.'
            raise AutomationError(error_message, detail=refresh_response.text)
        awr = extract_aw_response_id(refresh_response.text)
        self._update_content_state(awr, awpx)
        return AribaResponse(response=refresh_response, awr=awr)

    def _build_payload(self, awsn: str) -> dict[str, str]:
        """Build an element selected payload.

        Parameters
        ----------
        awsn : str
            Selected element ID.

        Returns
        -------
        dict[str, str]
            Payload.

        """
        payload = {
            'awssk': self.awssk,
            'awsn': awsn,
            'awr': self.awr,
            'awst': '0',
            'awsl': '0',
            'awii': 'xmlhttp',
        }
        return payload

    def _get_admin_page(self, main_page: BeautifulSoup) -> str:
        """Get the admin page.

        Parameters
        ----------
        main_page : BeautifulSoup
            Soup of the main page response content.

        Returns
        -------
        str
            Admin page response content.

        Raises
        ------
        AutomationError
            If admin page content could not be obtained.

        """
        response = self.session.get(
            BASE_PLATFORM_URL,
            params=self._build_payload(
                extract_admin_tab_element_id(main_page),
            ),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo redireccionar a la página de administración.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            ADMIN_PAGE_AWPX,
            'No se pudo obtener el contenido de la página de administración.',
        )
        self.awr = refreshed_content.awr
        return refreshed_content.response.text

    def _expand_admin_users_option(self, admin_page_content: str) -> str:
        """Expand admin users option.

        Parameters
        ----------
        admin_page_content : str
            Admin page response content.

        Returns
        -------
        str
            Expanded menu response content.

        Raises
        ------
        AutomationError
            If menu could not be expanded.

        """
        response = self.session.get(
            BASE_PLATFORM_URL,
            params=self._build_payload(
                extract_admin_users_options_element_id(admin_page_content),
            ),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo expandir el menú de administración de usuarios.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            ADMIN_PAGE_AWPX,
            (
                'No se pudo obtener el contenido del menú'
                ' de administración de usuarios.'
            ),
        )
        self.awr = refreshed_content.awr
        return refreshed_content.response.text

    def _get_users_form(self, expanded_menu_content: str) -> str:
        """Get the users form.

        Parameters
        ----------
        expanded_menu_content : str
            Expanded menu response content.

        Returns
        -------
        str
            Users form content.

        Raises
        ------
        AutomationError
            If user form could not be obtained.

        """
        response = self.session.get(
            BASE_PLATFORM_URL,
            params=self._build_payload(
                extract_admin_users_link_element_id(expanded_menu_content),
            ),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo redireccionar al formulario de consulta.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            USERS_FORM_AWPX,
            'No se pudo obtener el formulario de consulta de usuarios.',
        )
        self.awr = refreshed_content.awr
        return refreshed_content.response.text

    def _fetch_user(
        self,
        username: str,
        users_form_content: str,
    ) -> str:
        """Fetch a user by its username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        users_form_content : str
            Users form response content.

        Returns
        -------
        str
            Raw users data response.

        Raises
        ------
        AutomationError
            If user could not be fetched.

        """
        soup = get_html_soup(users_form_content)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de consulta de usuarios.',
        )
        input_422_name = extract_422_input_name(soup)
        button_id = extract_search_button_id(soup)
        payload = self._build_payload(button_id)
        form_data.data.update({input_422_name: username, **payload})
        params = {'awssk': self.awssk, 'awr': form_data.data['awr']}
        response = self.session.request(
            method=form_data.method,
            url=BASE_PLATFORM_URL,
            data=form_data.data,
            params=params,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            USERS_FORM_AWPX,
            'No se pudo obtener los datos principales del usuario.',
        )
        self.awr = refreshed_content.awr
        return refreshed_content.response.text

    def _extract_user_state(
        self,
        consulted_user_response_text: str,
    ) -> AribaUserState:
        """Extract the locked and active fields
        from the user data.

        Parameters
        ----------
        consulted_user_response_text : str
            Consulted user response content.

        Returns
        -------
        AribaUserState
            State of the user. Whether is locked and/or active.

        """
        soup = get_html_soup(consulted_user_response_text)
        actions_table = soup.find('table', {'class': USER_STATE_DIV_CLASS})
        if not actions_table:
            return AribaUserState(locked=True, active=False)
        locked = 'Desbloquear' in actions_table.text
        active = 'Desactivar' in actions_table.text
        return AribaUserState(locked=locked, active=active)

    def _get_consulted_user_page(self, consulted_user_content: str) -> str:
        """Get the consulted user page content awr.

        Parameters
        ----------
        consulted_user_content : str
            Consulted user response content.

        Returns
        -------
        str
            User details page.

        Raises
        ------
        AutomationError
            If user info could not be consulted.

        """
        soup = get_html_soup(consulted_user_content)
        form_data = extract_user_selection_form(soup)
        form_data.data.update(
            {
                'awr': self.awr,
                'awst': '200',
                'awsl': '0',
                'awssk': self.awssk,
                'awii': 'xmlhttp',
            }
        )
        params = {'awssk': self.awssk, 'awr': self.awr}
        response = self.session.request(
            method=form_data.method,
            url=BASE_PLATFORM_URL,
            data=form_data.data,
            params=params,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar toda la información del usuario.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            CONSULTED_USER_PAGE_AWPX,
            'No se pudo obtener el contenido con los datos del usuario.',
        )
        self.awr = extract_aw_response_id(refreshed_content.response.text)
        return refreshed_content.response.text

    def _fetch_user_groups(self, user_details: BeautifulSoup) -> str:
        """Fetch and extract the groups of the user.

        Parameters
        ----------
        user_details : BeautifulSoup
            Soup of the user details page.

        Returns
        -------
        str
            Page with the groups of the user.

        Raises
        ------
        AutomationError
            If groups of the user could not be obtained.

        """
        form_data = get_form_data(user_details)
        payload = self._build_payload(
            extract_groups_tab_id(user_details),
        )
        form_data.data.update(payload)
        params = {'awssk': self.awssk, 'awr': self.awr}
        response = self.session.request(
            method=form_data.method,
            url=BASE_PLATFORM_URL,
            data=form_data.data,
            params=params,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los grupos del usuario.',
                detail=response.text,
            )
        refreshed_content = self._refresh_content(
            CONSULTED_USER_PAGE_AWPX,
            'No se pudo obtener el contenido con los grupos del usuario.',
        )
        self.awr = extract_aw_response_id(refreshed_content.response.text)
        return refreshed_content.response.text

    def _send_all_consult_user_forms(
        self, username: str, main_page: BeautifulSoup
    ) -> str:
        """Send all the forms required to fetch the user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        main_page : BeautifulSoup
            Soup of the main page response content.

        Returns
        -------
        str
            Consulted user response content.

        """
        admin_page = self._get_admin_page(main_page)
        expanded_menu = self._expand_admin_users_option(admin_page)
        users_form = self._get_users_form(expanded_menu)
        return self._fetch_user(username, users_form)

    def _logout(self) -> None:
        """Perform the logout."""
        if not self.logout_button_id:
            return
        response = self.session.get(
            BASE_PLATFORM_URL,
            params=self._build_payload(self.logout_button_id),
        )
        match = re.search(r'https:[^\']+', response.text)
        if not match:
            return
        logout_url = match.group(0)
        response = self.session.get(logout_url)
        self.ariba_session.logout(response.text)

    @manager.enqueue('AribaModule')
    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> AribaResponseDto:
        """Consults the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        AribaResponseDto
            Requested user data.

        """
        try:
            login_last_response = self._authenticate()
            main_page = get_html_soup(login_last_response.text)
            self.awssk = extract_awssk_param(login_last_response)
            self.awr = extract_aw_response_id(login_last_response.text)
            consulted_user = self._send_all_consult_user_forms(
                username, main_page
            )
            user_details = get_html_soup(
                self._get_consulted_user_page(consulted_user)
            )
            self.logout_button_id = extract_logout_button_id(user_details)
            groups_page = self._fetch_user_groups(user_details)
            user_state = self._extract_user_state(consulted_user)
            return AribaResponseDto(
                active=user_state.active,
                locked=user_state.locked,
                groups=extract_user_groups(groups_page),
            )
        except Exception as e:
            if not isinstance(e, NotFoundError):
                self._logout()
            raise e
