<template>
  <spinner-component class="absolute-center" v-if="loading" />
  <div v-else
    :class="{ 'backdrop-blur': openConsultForm, 'column q-pa-md full-content-height': true }">

    <!-- Header -->
    <div class="col-shrink row justify-between">

      <!-- General options -->
      <div class="col-12 row items-center justify-between">
        <q-btn :label="t('exportDailyReport')" color="positive"
          @click="() => openDailyResultsForm = true">
          <q-tooltip class="text-body2" :offset="[10, 10]">
            {{ t('exportDailyReportTooltip') }}
          </q-tooltip>
        </q-btn>
        <q-btn :label="automationExecuted ? t('consultOtherUsers') : t('consultUsers')"
          color="primary" @click="reopenModal(false)" />
      </div>

      <!-- Results summary and options -->
      <div v-if="automationExecuted" class="col-12 row items-center justify-between">
        <div class="col-12 q-pt-sm">
          <q-separator spaced />
        </div>
        <div :class="{ 'text-primary': !$q.dark.isActive }">
          <p class="text-h5 text-bold q-mb-xs">
            {{ transaction.action == Action.consult ? t('queryResults') : t('deletionResults') }}
          </p>
          <p v-if="transaction.action == Action.consult" class="q-mb-xs"><span class="text-bold">
            {{ t('consultedUsers') }}: </span> {{ transaction.consultedUsers.length }}
          </p>
          <p v-else class="q-mb-xs">
            <span class="text-bold">{{ t('removedUsers') }}: </span>
            {{ transaction.removedUsers.length }}
          </p>
        </div>
        <div>
          <template v-if="enableRemoveUsers">
            <q-btn :label="t('removePermissions')" color="negative"
              :disable="!usersToBeRemovedTableData.length" @click="() => openRemoveForm = true">
              <q-tooltip class="text-body2" :offset="[10, 10]">
                {{ t('removePermissionsInfo') }}
              </q-tooltip>
            </q-btn>
            <q-btn :label="t('cancel')" color="positive" class="q-ml-sm"
              @click="cancelRemoveUsersProcess" />
          </template>
          <template v-else>
            <q-btn
              v-if="transaction.action == Action.consult && Object.keys(usersToBeRemoved).length && hasRemove"
              :label="t('removeUsers')" color="orange-8" @click="() => enableRemoveUsers = true" />
            <q-btn :label="t('exportAll')" color="positive" class="q-ml-sm" :disable="downloading"
              @click="downloadResultsFile" />
          </template>
        </div>
      </div>
    </div>

    <!-- Progress -->
    <progress-component v-if="runningAutomation" :progress="transaction.progress"
      :message="transaction.message" />

    <!-- Results -->
    <div v-else-if="transaction.consultedUsers.length || transaction.removedUsers.length"
      class="col q-pt-sm">

      <!-- Apps without remove users functionality -->
      <div v-if="manuallyDeletionApps.length && enableRemoveUsers" class="q-mb-md">
        <information-component text-color="black" information-type="info" type="card"
          :text="getManuallyDeletionAppsText()" />
      </div>

      <!-- Select all checkbox -->
      <div v-if="enableRemoveUsers" class="full-width text-left q-mb-md">
        <q-checkbox v-model="selectAllUserApps" color="primary" :label="t('selectAll')"
          @update:model-value="(value: any, _: Event) => selectAllUserAppsEvent(value)" />
      </div>

      <!-- Results by user -->
      <q-expansion-item
        v-for="(user, index) in (transaction.action == Action.consult ? transaction.consultedUsers : transaction.removedUsers)"
        :key="index" expand-separator icon="perm_identity" default-opened>
        <template v-slot:header>
          <q-item-section avatar>
            <q-avatar icon="perm_identity" color="secondary" text-color="white" size="md" />
          </q-item-section>

          <q-item-section>
            <p class="text-bold q-pa-none q-ma-none">{{ user.username }}</p>
          </q-item-section>
        </template>

        <user-results-summary v-if="!enableRemoveUsers" :action="transaction.action" :user="user"
          :hide-download-button="automationExecuted" :disable-download-button="downloading"
          @download-user-results="onDownloadUserResults(user.id, transaction.action)" />

        <div v-if="!enableRemoveUsers" class="row full-width q-py-md">
          <div v-for="(application, index) in results[user.username].data" :key="index"
            class="col-xs-12 col-sm-6 col-md-4 col-lg-3 q-pa-sm">
            <application-card-component :application="application"
              :removed-user="transaction.action == Action.remove" @on-expand="expandApplication" />
          </div>
        </div>
        <div v-else class="row full-width q-py-md">
          <div v-for="(application, index) in results[user.username].hasRemove" :key="index"
            class="col-xs-12 col-sm-6 col-md-4 col-lg-3 q-pa-sm">
            <application-card-component
              v-if="usersToBeRemoved[user.username] && usersToBeRemoved[user.username][application.application]"
              v-model="usersToBeRemoved[user.username][application.application].removeFrom"
              :application="application" :show-select="enableRemoveUsers"
              :removed-user="transaction.action == Action.remove"
              @on-toggle="toggleSelectAllUserApps" @on-expand="expandApplication" />
          </div>
        </div>
      </q-expansion-item>
    </div>

    <!-- Main view (information) -->
    <main-view-component v-else-if="!openConsultForm" />
  </div>

  <!-- Expand application modal -->
  <q-dialog v-if="selectedApplication" v-model="expand" full-width full-height>
    <application-card-component :application="selectedApplication"
      :removed-user="transaction.action == Action.remove" @on-close="closeApplication" on-dialog />
  </q-dialog>

  <!-- Forms -->
  <consult-users-form-component v-model="openConsultForm" @submit="onConsultUsers" />
  <remove-users-form-component v-model="openRemoveForm" :users-to-be-removed="usersToBeRemoved"
    :users-to-be-removed-table-data="usersToBeRemovedTableData" @submit="onRemoveUsers"
    @cancel="cancelRemoveUsersProcess" />

  <!-- Export daily results modal -->
  <daily-results-modal-component v-model="openDailyResultsForm" @submit="onDownloadDailyResults" />

  <!-- Last action results modal -->
  <last-action-results-modal-component v-model="openLastActionResults"
    @show="onShowLastActionResults" />

  <!-- Errors modal -->
  <modal-component v-model="showErrorModal" type="error" :title="t('error')"
    :message="errorModalMessage" :close-callback="() => showErrorModal = false" />
</template>

<script setup lang="ts">
//#region imports
import { AxiosError, AxiosProgressEvent } from 'axios';
import { QSpinnerGears, useQuasar } from 'quasar';
import { computed, onBeforeMount, onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import type { AutomationFormData } from 'src/forms/automation/automationForm';
import { consultedUsersRawData, removedUsersRawData } from 'src/lib/automationData';
import type { Application, DailyResultsForm, RemoveUsersData, RemoveUsersTableData, UsersToBeRemovedMap } from 'src/lib/interfaces';
import { cleanObject, extractJSONFromString, getAxiosErrorMessage, getErrorFromBlob } from 'src/lib/tools';
import UserSession from 'src/lib/userSession';
import { Action, Transaction, TransactionProgress, TransactionTools } from 'src/models/transaction';
import { consultUsers, removeUsers } from 'src/services/automationService';
import {
  exportReport,
  exportTransactionResults,
  exportUserResults,
  getCurrentTransaction,
  getTransaction,
  getTransactionProgress
} from 'src/services/transactionService';

import ApplicationCardComponent from 'src/components/automation/ApplicationCardComponent.vue';
import ConsultUsersFormComponent from 'src/components/automation/ConsultUsersFormComponent.vue';
import DailyResultsModalComponent from 'src/components/automation/DailyResultsModalComponent.vue';
import LastActionResultsModalComponent from 'src/components/automation/LastActionResultsModalComponent.vue';
import MainViewComponent from 'src/components/automation/MainViewComponent.vue';
import ProgressComponent from 'src/components/automation/ProgressComponent.vue';
import RemoveUsersFormComponent from 'src/components/automation/RemoveUsersFormComponent.vue';
import UserResultsSummary from 'src/components/automation/UserResultsSummary.vue';
import InformationComponent from 'src/components/common/InformationComponent.vue';
import ModalComponent from 'src/components/common/ModalComponent.vue';
import SpinnerComponent from 'src/components/common/SpinnerComponent.vue';
//#endregion

//#region types
interface ApplicationResults {
  [username: string]: {
    data: Application[];
    hasRemove: Application[];
  }
}
//#endregion

const { t } = useI18n();

const $q = useQuasar();

const loading = ref(true);

const downloading = ref(false);

const automationExecuted = ref(false);

const showErrorModal = ref(false);

const errorModalMessage = ref('');

const runningAutomation = ref(false);

const openConsultForm = ref(false);

const openRemoveForm = ref(false);

const openLastActionResults = ref(false);

const openDailyResultsForm = ref(false);

const expand = ref(false);

const enableRemoveUsers = ref(false);

const selectedApplication = ref<Application>();

const lastTransactionId = ref<string | null>(null);

const selectAllUserApps = ref<boolean>(false);

const transaction = reactive<Transaction>(TransactionTools.getEmptyTransaction());

const results = reactive<ApplicationResults>({});

const usersToBeRemoved = reactive<UsersToBeRemovedMap>({});

const manuallyDeletionApps = reactive<string[]>([]);

const hasRemove = computed<boolean>(() => {
  return UserSession.hasRemove();
});

const usersToBeRemovedTableData = computed(() => {
  const rows: RemoveUsersTableData[] = [];
  Object.entries(usersToBeRemoved).forEach(([user, apps]) => {
    const applications = Object.values(apps).filter(fields => fields.removeFrom).map(fields => fields.name);
    if (applications.length) rows.push({ user: user, applications: applications.join(', ') });
  });
  return rows;
});

let interval: NodeJS.Timeout;

function toggleSelectAllUserApps(): void {
  selectAllUserApps.value = false;
}

function selectAllUserAppsEvent(value: boolean): void {
  loading.value = true;
  Object.values(usersToBeRemoved).forEach(apps => {
    Object.values(apps).forEach(fields => fields.removeFrom = value);
  });
  loading.value = false;
}

function cancelRemoveUsersProcess(): void {
  loading.value = true;
  toggleSelectAllUserApps();
  enableRemoveUsers.value = false;
  Object.values(usersToBeRemoved).forEach(apps => {
    Object.values(apps).forEach(fields => fields.removeFrom = false);
  });
  loading.value = false;
}

function expandApplication(application: Application): void {
  selectedApplication.value = application;
  expand.value = true;
}

function closeApplication(): void {
  selectedApplication.value = undefined;
  expand.value = false;
}

function getManuallyDeletionAppsText(): string {
  return manuallyDeletionApps.length > 0 ? t('manuallyDeletionAppsText', { apps: manuallyDeletionApps.join(', ') }) : t('none');
}

function setConsultResults(): void {
  manuallyDeletionApps.splice(0);
  transaction.consultedUsers.forEach(user => {
    results[user.username] = { data: [], hasRemove: [] };
    usersToBeRemoved[user.username] = {};
    user.data.forEach(app => {
      results[user.username].data.push(app);
      if (!app.userNotFound && !app.error && !app.ignored && !app.warning && app.hasPrivileges) {
        if (app.hasRemove) {
          results[user.username].hasRemove.push(app);
          usersToBeRemoved[user.username][app.application] = { removeFrom: false, name: app.applicationName };
        } else if (!manuallyDeletionApps.includes(app.applicationName)) {
          manuallyDeletionApps.push(app.applicationName);
        }
      }
    });
  });
}

function setResults(): void {
  if (transaction.action == Action.consult) {
    setConsultResults();
  } else {
    transaction.removedUsers.forEach(user => {
      results[user.username] = { data: [], hasRemove: [] };
      Object.assign(results[user.username].data, user.data);
    });
  }
}

function restartData(): void {
  openConsultForm.value = false;
  automationExecuted.value = false;
  enableRemoveUsers.value = false;
  selectAllUserApps.value = false;
  TransactionTools.cleanTransaction(transaction);
  cleanObject(results);
  cleanObject(usersToBeRemoved);
  runningAutomation.value = true;
}

function processError(error: AxiosError): void {
  runningAutomation.value = false;
  if (error.response?.status == 401) return;
  const message = getAxiosErrorMessage(error as AxiosError);
  errorModalMessage.value = message ? message : t('anAutomationErrorHasOccurred');
  showErrorModal.value = true;
}

async function onDownloadDailyResults(form: DailyResultsForm): Promise<void> {
  openDailyResultsForm.value = false;
  try {
    downloading.value = true;
    $q.notify({
      spinner: QSpinnerGears,
      message: t('downloadingDailyResultsFile'),
      color: 'positive',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
    await exportReport(
      form.exportType,
      form.hasPrivileges,
      form.includeNotFound,
      form.includeErrors
    );
    downloading.value = false;
  } catch (error: unknown) {
    downloading.value = false;
    $q.notify({
      message: await getErrorFromBlob(error as AxiosError),
      color: 'negative',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
  }
}

async function downloadResultsFile(): Promise<void> {
  try {
    downloading.value = true;
    $q.notify({
      spinner: QSpinnerGears,
      message: t('downloadingResultsFile'),
      color: 'positive',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
    await exportTransactionResults(transaction.id);
    downloading.value = false;
  } catch (error: unknown) {
    downloading.value = false;
    $q.notify({
      message: await getErrorFromBlob(error as AxiosError),
      color: 'negative',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
  }
}

async function onDownloadUserResults(userId: string, action: Action): Promise<void> {
  try {
    downloading.value = true;
    $q.notify({
      spinner: QSpinnerGears,
      message: t('downloadingResultsFile'),
      color: 'positive',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
    await exportUserResults(userId, action);
    downloading.value = false;
  } catch (error: unknown) {
    downloading.value = false;
    $q.notify({
      message: await getErrorFromBlob(error as AxiosError),
      color: 'negative',
      position: 'top',
      timeout: 6000,
      actions: [
        { icon: 'close', color: 'white', round: true, handler: void 0 }
      ]
    });
  }
}

function transactionProgressCallback(progressEvent: AxiosProgressEvent): void {
  try {
    const responseText: string = progressEvent.event.target.responseText;
    const lastMessage = responseText.trim().split('data: ').at(-1);
    if (!lastMessage) return;
    const transactionProgress: TransactionProgress | null = extractJSONFromString(lastMessage);
    if (!transactionProgress) return;
    transaction.progress = transactionProgress.progress;
    if (transactionProgress.state == 'error') {
      return;
    } else {
      transaction.message = transactionProgress.message;
    }
  } catch {
    return;
  }
}

async function getUpdatedTransactionData(): Promise<void> {
  try {
    transaction.usernames.splice(0);
    if (transaction.action == Action.consult) transaction.consultedUsers.splice(0);
    else transaction.removedUsers.splice(0);
    Object.assign(transaction, await getTransaction(transaction.id));
    if (transaction.state == 'done') {
      automationExecuted.value = true;
      runningAutomation.value = false;
      clearInterval(interval);
    } else if (transaction.state == 'error') {
      runningAutomation.value = false;
      const message = transaction.message;
      errorModalMessage.value = message ? message : t('anAutomationErrorHasOccurred');
      showErrorModal.value = true;
      clearInterval(interval);
    }
    setResults();
  } catch (error: unknown) {
    processError(error as AxiosError);
    clearInterval(interval);
  }
}

async function showTransactionProgress(): Promise<void> {
  try {
    await getTransactionProgress(transaction.id as string, transactionProgressCallback);
    interval = setInterval(async () => {
      await getUpdatedTransactionData();
    }, 5000);
  } catch (error: unknown) {
    processError(error as AxiosError);
    clearInterval(interval);
  }
}

function checkUseRawAutomationResponse(action: Action, setTestResults = true): boolean {
  UserSession.serverHealth = {
    status: true,
    message: 'OK',
    authenticated: true,
    enableBotRetiros: true,
  }
  if (process.env.USE_RAW_AUTOMATION_RESPONSE == 'true') {
    if (!setTestResults) {
      loading.value = false;
      return true;
    }
    restartData();
    Object.assign(transaction, JSON.parse(action == Action.consult ? consultedUsersRawData : removedUsersRawData));
    setResults();
    automationExecuted.value = true;
    runningAutomation.value = false;
    loading.value = false;
    return true;
  }
  return false;
}

async function onConsultUsers(form: AutomationFormData): Promise<void> {
  try {
    if (checkUseRawAutomationResponse(Action.consult)) return;
    restartData();
    Object.assign(transaction, await consultUsers(form));
    await showTransactionProgress();
  } catch (error: unknown) {
    processError(error as AxiosError);
  }
}

async function checkCurrentTransaction(): Promise<boolean> {
  try {
    if (checkUseRawAutomationResponse(Action.consult, false)) return false;
    const currentTransaction = await getCurrentTransaction();
    if (!currentTransaction) {
      loading.value = false;
      return false;
    }
    restartData();
    Object.assign(transaction, currentTransaction);
    loading.value = false;
    await showTransactionProgress();
    return true;
  } catch (error: unknown) {
    loading.value = false;
    return false;
  }
}

async function onShowLastActionResults(): Promise<void> {
  openLastActionResults.value = false;
  try {
    if (checkUseRawAutomationResponse(Action.consult, false)) return;
    if (!lastTransactionId.value) {
      errorModalMessage.value = t('lastActionResultsNotFound');
      showErrorModal.value = true;
      return;
    }
    const lastTransaction = await getTransaction(lastTransactionId.value);
    restartData();
    Object.assign(transaction, lastTransaction);
    setResults();
    automationExecuted.value = true;
    runningAutomation.value = false;
    loading.value = false;
  } catch (error: unknown) {
    processError(error as AxiosError);
  }
}

async function onRemoveUsers(data: RemoveUsersData): Promise<void> {
  try {
    openRemoveForm.value = false;
    if (checkUseRawAutomationResponse(Action.remove)) return;
    restartData();
    Object.assign(transaction, await removeUsers(data));
    await showTransactionProgress();
  } catch (error: unknown) {
    processError(error as AxiosError);
  }
}

function reopenModal(clean = false): void {
  if (clean) {
  }
  openConsultForm.value = true;
}

onMounted(async (): Promise<void> => {
  const checked = await checkCurrentTransaction();
  lastTransactionId.value = $q.sessionStorage.getItem<string>('lastTransactionId');
  if (!checked && lastTransactionId.value) {
    openLastActionResults.value = true;
  }
});

onBeforeMount((): void => {
  clearInterval(interval);
});
</script>

<style scoped>
.modal-icon {
  cursor: pointer;
}

.backdrop-blur {
  filter: blur(5px);
}

.border-radius {
  border-radius: 10px;
}
</style>
