<template>
  <router-view v-if="ready" />
  <div class="flex flex-center window-height window-width" v-else>
    <spinner-component />
  </div>
</template>

<script setup lang="ts">
//#region imports
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

import UserSession from 'src/lib/userSession';

import SpinnerComponent from 'src/components/common/SpinnerComponent.vue';
//#endregion

const $q = useQuasar();

const { t } = useI18n();

const router = useRouter();

const ready = ref<boolean>(false);

const checkServerHealth = async (): Promise<void> => {
  const serverHealth = await UserSession.getServerHealth();
  if (!serverHealth.status) {
    router.push('/health');
  } else if (!serverHealth.authenticated && !UserSession.onLoginRoute()) {
    const message = UserSession.getSessionToken() ? t('signInAgainPlease') : undefined;
    await UserSession.logout(message);
  } else if (!UserSession.hasConsult()) {
    router.push('/forbidden');
  }
}

const setDarkMode = (): void => {
  $q.dark.set($q.localStorage.getItem<boolean>('dark') ?? false);
}

onMounted(async (): Promise<void> => {
  await router.isReady();
  await checkServerHealth();
  setDarkMode();
  ready.value = true;
});
</script>
