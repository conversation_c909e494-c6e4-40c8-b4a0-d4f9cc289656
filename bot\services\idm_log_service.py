from beanie import PydanticObjectId

from dtos.idm_log_dto import (
    IDMLogRequestDto,
    IDMLogResponseDto,
)
from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.export import ExportData
from lib.filter import Filter, FormatFilter
from lib.pagination import Pagination
from models.idm_log import IDMLog


class IDMLogService(BaseInjectable, BaseService):
    """Service to create and list."""

    __filter: Filter[IDMLog]

    def __init__(self, export_data: ExportData, _filter: Filter) -> None:
        """Create a dependency object of this service.

        Parameters
        ----------
        export_data : ExportData
            Injected dependency of the exportation module.
        logs_exporter : LogsExporter
            Injected dependency of the logs exportation module.
        _filter : Filter
            Injected dependency of the mongo filters module.

        """
        super().__init__()
        self.__export_data = export_data
        self.__filter = _filter

    async def list_all(
        self,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[IDMLogResponseDto]:
        """Get the list of logs.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the logs,
            by default None.

        Returns
        -------
        Pagination[IDMLogResponseDto]
            Paginated list of logs.

        """
        query = IDMLog.find(
            IDMLog.isDeleted == False,  # noqa: E712
            fetch_links=True,
        )

        if filter_data is not None:
            query = self.__filter.create(filter_data, query, fetch_link=True)

        pagination = Pagination[IDMLogResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = IDMLogResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def get_logs_by_transaction_id(
        self,
        transaction_id: str,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[IDMLogResponseDto]:
        """Retrieve logs by transaction ID.

        Parameters
        ----------
        transaction_id : str
            Transaction ID.

        Returns
        -------
        Pagination[IDMLogResponseDto]
            Paginated list of logs.

        """
        query = IDMLog.find(
            IDMLog.isDeleted == False,  # noqa: E712
            IDMLog.transactionId == PydanticObjectId(transaction_id),
            fetch_links=True,
        )

        if filter_data is not None:
            query = self.__filter.create(filter_data, query, fetch_link=True)

        pagination = Pagination[IDMLogResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = IDMLogResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def create(
        self, log_dto: IDMLogRequestDto
    ) -> IDMLogResponseDto:
        """Create a log.

        Parameters
        ----------
        log_dto : IDMResponseLogRequestDto
            Log's data

        Returns
        -------
        IDMLogResponseDto
            Created log.

        """
        new_log = IDMLog(**log_dto.to_dict())
        return IDMLogResponseDto.from_orm(await new_log.create())

    async def retrieve(self, id: str) -> IDMLogResponseDto | None:
        """Retrieve a log.

        Parameters
        ----------
        id : str
            Log id.

        Returns
        -------
        IDMLogResponseDto | None
            Log data if found.

        """
        log = await IDMLog.get(PydanticObjectId(id))
        if log is None or log.isDeleted:
            return None
        return IDMLogResponseDto.from_orm(log)
