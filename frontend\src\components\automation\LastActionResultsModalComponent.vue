<template>
  <modal-component v-model="modelValue" :title="t('showLastActionResults')"
    :message="t('wantToSeeTheLastActionResults')" :actions="[
      {
        label: t('no'),
        color: 'warning',
        callback: () => modelValue = false,
      },
      {
        label: t('yes'),
        color: 'positive',
        callback: () => emit('show'),
      }
    ]" />
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import ModalComponent from 'src/components/common/ModalComponent.vue';
//#endregion

//#region types
interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', showModal: boolean): void;
  (e: 'show'): void;
}
//#endregion

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const { t } = useI18n();

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit('update:modelValue', value);
  }
});
</script>
