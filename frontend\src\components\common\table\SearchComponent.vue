<template>
  <div>
    <q-select
      v-if="props.optionsConditions && !props.hideSelectCondition && props.expandedConditionsFilter"
      outlined
      dense
      class="q-pb-sm"
      size="1"
      :label="t('condition')"
      v-model="condition"
      map-options
      @update:model-value="emits('onValue', filter, condition.value)"
      :options="(typeof props.optionsConditions == 'boolean') ? defaultOptionsConditions : optionsFromProps(props.optionsConditions)" />
    <q-select
      v-if="(props.select)"
      outlined
      dense
      v-model="filter"
      size="1"
      :options="props.optionsSelect"
      :label="props.label"
      map-options
      @update:model-value ="emits('onValue', filter, condition.value)"
      @keyup.enter="emits('onSearch')">
      <template v-slot:append>
        <q-icon v-if="filter !== ''" name="close" @click="clearField" class="cursor-pointer" />
      </template>
    </q-select>
    <q-input
      v-else-if="props.type === 'time'"
      outlined
      dense
      v-model="filter"
      size="1"
      :label="props.label"
      mask="time"
      fill-mask
      @keyup.enter="emits('onSearch')"
      @keyup="emits('onValue', filter, condition.value)">
      <template v-slot:prepend>
        <q-icon name="access_time" class="cursor-pointer">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-time v-model="filter" format24h>
              <div class="row items-center justify-end">
                <q-btn v-close-popup :label="t('close')" color="primary" flat />
              </div>
            </q-time>
          </q-popup-proxy>
        </q-icon>
      </template>
      <template v-slot:append>
        <q-icon v-if="filter !== ''" name="close" @click="clearField" class="cursor-pointer" />
      </template>
    </q-input>
    <q-input
      v-else-if="props.type === 'date'"
      outlined
      dense
      v-model="filter"
      size="1"
      :label="props.label"
      mask="####-##-##"
      fill-mask
      @keyup.enter="emits('onSearch')"
      @keyup="emits('onValue', filter, condition.value)">
      <template v-slot:prepend>
        <q-icon name="event" class="cursor-pointer">
          <q-popup-proxy class="q-ma-xl" cover transition-show="scale" transition-hide="scale">
            <q-date v-model="filter" :title="t('months')" :subtitle="t('years')">
              <div class="row items-center justify-end">
                <q-btn v-close-popup :label="t('close')" color="primary" flat />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </template>
      <template v-slot:append>
        <q-icon v-if="filter !== ''" name="close" @click="clearField" class="cursor-pointer" />
      </template>
    </q-input>
    <q-input
      v-else
      v-model="filter"
      outlined
      dense
      size="1"
      :label="props.label"
      :type="props.type"
      @keyup.enter="emits('onSearch')"
      @keyup="emits('onValue', filter, condition.value)">
      <template v-slot:append>
        <q-icon v-if="filter !== ''" name="close" @click="clearField" class="cursor-pointer" />
      </template>
    </q-input>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
//#region imports
import { onMounted, ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import type { Search } from 'src/lib/interfaces';
//#endregion

//#region types
interface SearchEmits {
  (e: 'onSearch'): void;
  (e: 'onValue', filter: string, condition: string): void;
  (e: 'onClear'): void;
}

interface ConditionsFilter {
    label: string;
    value: string;
}

interface SearchProps {
  searchObject?: Search;
  clearTrigger?: number;
  colName?: string;
  select?: boolean;
  hideSelectCondition?: boolean;
  optionsSelect?: any[];
  label?: string;
  type?: 'number' | 'time' | 'text' | 'date';
  optionsConditions?: string[] | boolean;
  expandedConditionsFilter?: boolean;
}
//#endregion

const { t } = useI18n();

const props = withDefaults(defineProps<SearchProps>(),{
  label: '',
  type: 'text',
  select: false,
  hideSelectCondition: false,
  expandedConditionsFilter: false
});

const emits = defineEmits<SearchEmits>();

const filter = ref<string>('');

const clearTrigger = computed( () => props.clearTrigger);

const condition = ref<ConditionsFilter>({label: t('equal'), value: 'eq'});

const clearField = () => {
  emits('onClear');
  filter.value = '';
}

const defaultOptionsConditions: ConditionsFilter[] = [
  {
    label: t('equal'),
    value: 'eq'
  }, {
    label: t('notEqual'),
    value: 'ne'
  }, {
    label: t('greaterThan'),
    value: 'gt'
  }, {
    label: t('lowerThan'),
    value: 'lt'
  }, {
    label: t('greaterEqualThan'),
    value: 'gte'
  }, {
    label: t('lowerEqualThan'),
    value: 'lte'
  }
];

const optionsFromProps = (filterProps: string[]): ConditionsFilter[] => {
  let optionsConditions: ConditionsFilter[] = [];
  for (const conditionValue of filterProps) {
  switch(conditionValue) {
      case 'eq':
        optionsConditions.push({label: t('equal'), value: conditionValue});
        break;
      case 'ne':
        optionsConditions.push({label: t('notEqual'), value: conditionValue});
        break;
      case 'gt':
        optionsConditions.push({label: t('greaterThan'), value: conditionValue});
        break;
      case 'lt':
        optionsConditions.push({label: t('lowerThan'), value: conditionValue});
        break;
      case 'gte':
        optionsConditions.push({label: t('greaterEqualThan'), value: conditionValue});
        break;
      case 'lte':
        optionsConditions.push({label: t('lowerEqualThan'), value: conditionValue});
        break;
      case 'regex':
        optionsConditions.push({label: t('contains'), value: conditionValue});
        break;
      default:
        break;
    }
  }
  return optionsConditions;
}

watch(clearTrigger, () => {
  filter.value = '';
});

onMounted((): void => {
  condition.value = props.optionsConditions && props.optionsConditions instanceof Array ? optionsFromProps(props.optionsConditions)[0] : defaultOptionsConditions[0];
  if(props.searchObject && props.colName && props.searchObject[props.colName]){
    filter.value = props.searchObject[props.colName].value as string;
  }
});
</script>
