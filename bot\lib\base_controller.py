import typing
from typing_extensions import Protocol
from typing import Callable, Coroutine, Any, cast
from functools import wraps
from starlette.requests import Request as StarletteRequest

from i18n import I18N
from lib.responses import (
    MediaResponse,
    ErrorResponse,
    ChunkEncodedMediaResponse,
)
from lib.export import File
from lib.injectable import Injectable
from lib.user_session import UserSession


class Wrapped:
    func: Callable
    path: str
    method: str

    def __call__(self, *args: Any) -> Coroutine[Any, Any, Any]: ...


def get(path: str):
    """Create a GET endpoint.

    Parameters
    ----------
    path : str
        Endpoint route.
    """

    def decorator(method: Callable):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            return await method(self, *args, **kwargs)

        typing.cast(Wrapped, wrapper).func = method
        typing.cast(Wrapped, wrapper).path = path
        typing.cast(Wrapped, wrapper).method = 'get'
        return wrapper

    return decorator


def post(path: str):
    """Create a POST endpoint.

    Parameters
    ----------
    path : str
        Endpoint route.
    """

    def decorator(method: Callable):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            return await method(self, *args, **kwargs)

        typing.cast(Wrapped, wrapper).func = method
        typing.cast(Wrapped, wrapper).path = path
        typing.cast(Wrapped, wrapper).method = 'post'
        return wrapper

    return decorator


def put(path: str):
    """Create a PUT endpoint.

    Parameters
    ----------
    path : str
        Endpoint route.
    """

    def decorator(method: Callable):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            return await method(self, *args, **kwargs)

        typing.cast(Wrapped, wrapper).func = method
        typing.cast(Wrapped, wrapper).path = path
        typing.cast(Wrapped, wrapper).method = 'put'
        return wrapper

    return decorator


def delete(path: str):
    """Create a DELETE endpoint.

    Parameters
    ----------
    path : str
        Endpoint route.
    """

    def decorator(method: Callable):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            return await method(self, *args, **kwargs)

        typing.cast(Wrapped, wrapper).func = method
        typing.cast(Wrapped, wrapper).path = path
        typing.cast(Wrapped, wrapper).method = 'delete'
        return wrapper

    return decorator


class ExportCallback(Protocol):
    def __call__(self, *args: Any) -> Coroutine[Any, Any, File]: ...


class Request(StarletteRequest):
    user: UserSession


class BaseController:
    """Base class for the controllers."""

    request: Request
    injectable: Injectable

    def __init__(self, request: Request, injectable: Injectable) -> None:
        self.request = request
        self.injectable = injectable

    def prev(self) -> None:
        """Pre-configuration function.

        Called before executing the endpoints.
        """
        cast(
            I18N, self.injectable.get(I18N)
        ).language = self.request.headers.get('Accept-Language')

    @property
    def language(self) -> str | None:
        """the current language stored in the request state."""
        if not hasattr(self.request.state, 'language'):
            return None
        return self.request.state.language

    def _get_export_parameters(self, default_limit: int) -> tuple[int, int]:
        """Get the export parameters such as the skip and limit numbers.

        Parameters
        ----------
        default_limit : int
            Default limit.

        Returns
        -------
        tuple[int, int]
            Two-elements tuple with the skip and limit number.

        """
        try:
            start_index_param = int(
                self.request.query_params.get('start_index', 0)
            )
        except Exception:
            start_index_param = 0

        try:
            limit_param = int(
                self.request.query_params.get('limit', default_limit)
            )
        except Exception:
            limit_param = default_limit

        start_index = start_index_param if start_index_param >= 0 else 0
        return start_index, limit_param

    async def _export(
        self, export_callback: ExportCallback, *args, **kwargs
    ) -> MediaResponse | ErrorResponse:
        """Call an export function.

        Returns a ChunkEncodedMediaResponse if the file is stored
        in disk. Otherwise, returns a MediaResponse with the file
        content stream.

        Parameters
        ----------
        export_callback : ExportCallback
            Export function.

        Returns
        -------
        ChunkEncodedMediaResponse
            Streams a file stored in the disk.
        MediaResponse
            Streams a file content.
        ErrorResponse
            Error response containing the list of error messages.

        """
        file = await export_callback(*args, *kwargs)
        if file.path:
            return ChunkEncodedMediaResponse(
                file.path, headers=file.headers, media_type=file.media_type
            )
        return MediaResponse(
            file.stream, headers=file.headers, media_type=file.media_type
        )
