from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response

from lib.base_controller import Request


class BaseMiddleware(BaseHTTPMiddleware):
    """Basic middleware template."""

    request: Request
    response: Response

    async def before_dispatch(self) -> None:
        """Task to run before dispatching the response."""

        pass

    async def after_dispatch(self) -> None:
        """Task to run after dispatching the response."""

        pass

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Dispatches the response.\n
        NOTE: Do not overwrite this method unless
        you need to implement a custom dispatching method.

        Parameters
        ----------
        request : Request
            Request.
        call_next : RequestResponseEndpoint
            Endpoint response function.

        Returns
        -------
        Response
            Response.
        """
        self.request = request
        await self.before_dispatch()
        self.response = await call_next(self.request)
        await self.after_dispatch()
        return self.response
