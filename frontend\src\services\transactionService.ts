//#region imports
import { api } from 'src/boot/axios';
import { AxiosResponse } from 'axios';

import { TransactionTools } from 'src/models/transaction';
import type{ TransactionProgressCallback } from 'src/lib/interfaces';
import{ Action, Transaction, TransactionResponse } from 'src/models/transaction';
import type { Pagination, Search } from 'src/lib/interfaces';
import { downloadFromBlob } from 'src/lib/tools';
//#endregion

export const listTransactions = async (page = 1, filter?: Search): Promise<Pagination<Transaction>> => {
  const response: AxiosResponse<Pagination<TransactionResponse>> = await api.get(`transactions?page=${page}&q=${JSON.stringify(filter ?? '')}`);
  return { ...response.data, data: TransactionTools.loadMany(response.data.data) }
}

export const getTransaction = async (id: string): Promise<Transaction> => {
  const response: AxiosResponse<TransactionResponse> = await api.get(`transactions/${id}`);
  return TransactionTools.load(response.data);
}

export const getCurrentTransaction = async (): Promise<Transaction | null> => {
  const response: AxiosResponse<TransactionResponse | null> = await api.get('transactions/progress/by_creator');
  return response.data ? TransactionTools.load(response.data) : null;
}

export const getTransactionProgress = async (id: string, callback: TransactionProgressCallback): Promise<void> => {
  await api.get(`transactions/${id}/progress`, {onDownloadProgress: (processEvent) => {
    callback(processEvent);
  }});
}

export const exportTransactionResults = async (id: string): Promise<void> => {
  const response: AxiosResponse<BlobPart> = await api.get(`transactions/${id}/export`, { responseType: 'blob' });
  downloadFromBlob(response.data, response.headers['content-disposition']);
}

export const exportReport = async (
  action: 'consult' | 'remove' | 'all' | 'previous_deletions' = 'consult',
  hasPrivileges = true,
  includeNotFound = false,
  includeErrors = false
): Promise<void> => {
  if(hasPrivileges && action == 'consult') {
    includeNotFound = false;
  }
  const queryString = `action=${action}&hasPrivileges=${hasPrivileges}&includeNotFound=${includeNotFound}&includeErrors=${includeErrors}`;
  const response: AxiosResponse<BlobPart> = await api.get(`transactions/export/report?${queryString}`, { responseType: 'blob' });
  downloadFromBlob(response.data, response.headers['content-disposition']);
}

export const exportUserResults = async (id: string, action: Action): Promise<void> => {
  const endpoint = action == Action.consult ? 'consulted_users' : 'removed_users';
  const response: AxiosResponse<BlobPart> = await api.get(`${endpoint}/${id}/export`, { responseType: 'blob' });
  downloadFromBlob(response.data, response.headers['content-disposition']);
}
