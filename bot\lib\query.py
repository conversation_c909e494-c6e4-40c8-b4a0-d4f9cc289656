from inspect import isclass
from typing import Any, TypeVar, Generic, Mapping
from beanie.odm.queries.find import <PERSON><PERSON><PERSON>
from beanie.odm.queries.aggregation import AggregationQuery
from beanie.odm.enums import SortDirection
from beanie.odm.utils.find import construct_lookup_queries

from lib.base_model import BaseModel
from lib.base_dto import BaseResponseDto
from lib.base_injectable import BaseInjectable
from lib.filter import Filter, FormatFilter
from lib.translate import Translate


T = TypeVar('T', bound=BaseModel)

ProjectionModel = TypeVar('ProjectionModel', bound=BaseResponseDto)


class Query(Generic[T], BaseInjectable):
    """Creates complex Mongo queries implementing
    fetching links, projection and data translation.
    """

    __filter: Filter[T]
    __translate: Translate[T]
    __properties: dict[str, Any]

    def __init__(self, _filter: Filter, _translate: Translate) -> None:
        """Creates a dependency object of this module.

        Parameters
        ----------
        _filter : Filter
            Injected dependency of the Filter module.
        _translate : Translate
            Injected dependency of the translate module.
        """

        super().__init__()
        self.__filter = _filter
        self.__translate = _translate
        self.__properties = {}

    def __get_fetch_links_aggregations(self, aggregations: list[dict[str, Any]]) -> None:
        """Gets aggregations to fetch DBRefs.

        NOTE: The "fetch_link" argument does not support
        fetching DBRefs.

        Parameters
        ----------
        aggregations : list[dict[str, Any]]
            Current aggregations list.
        """

        for link, link_data in self.__properties.items():
            all_of = link_data.get('allOf')
            if all_of:
                collection = all_of[0]['$ref'].split('/')[-1]
                aggregations.extend([
                    {'$addFields': {f'{link}': {'$ifNull': [f'${link}.$id', f'${link}']}}},
                    {'$lookup': {'from': f'{collection}', 'localField': f'{link}', 'foreignField': '_id', 'as': f'{link}'}},
                    {'$addFields': {f'{link}': {'$first': f'${link}'}}}
                ])

    def __get_translation_aggregations(self, aggregations: list[dict[str, Any]], model: type[T]) -> None:
        """Gets translation aggregations.

        NOTE: Model must implement the TranslationConfig inner class
        with the "data" attribute in order to perform any translation.

        Parameters
        ----------
        aggregations : list[dict[str, Any]]
            Current aggregations list.
        model : type[T]
            Database model.
        """

        if 'TranslationConfig' in model.__dict__ and isclass(model.TranslationConfig):
            data = model.TranslationConfig.__dict__.get('data')
            if data and isinstance(data, dict):
                for attr in data.items():
                    if attr[0] in self.__properties:
                        aggregations.append(self.__translate.create({f'{attr[0]}': attr[1]}))

    def __get_projection_aggregations(self, aggregations: list[dict[str, Any]], model: type[T]) -> None:
        """Gets projection aggregations.

        NOTE: Model must implement the ProjectionConfig inner class
        in order to perform any projection.

        Parameters
        ----------
        aggregations : list[dict[str, Any]]
            Current aggregations list.
        model : type[T]
            Database model.
        """

        if 'ProjectionConfig' in model.__dict__ and isclass(model.__dict__['ProjectionConfig']):
            for attr in model.ProjectionConfig.__dict__.items():
                projection = attr[1].split('.') if isinstance(attr[1], str) and '.' in attr[1] else (attr[1], None)
                if projection[0] in self.__properties:
                    if projection[1]:
                        aggregations.append({'$addFields': {f'{attr[0]}': {'$getField': {'field': {'$literal': f'{projection[1]}'}, 'input': f'${projection[0]}'}}}})
                    else:
                        aggregations.append({'$addFields': {f'{attr[0]}': f'${projection[0]}'}})

    def create(self, model: type[T], *args: Mapping[str, Any] | bool, start_index: int | None = None, limit: int | None = None, format_filter: FormatFilter | None = None, fetch_links: bool = False, projection_model: type[ProjectionModel] | None = None, sort: str | tuple[str, SortDirection] | list[tuple[str, SortDirection]] | None = None) -> FindMany[T] | FindMany[ProjectionModel] | AggregationQuery[ProjectionModel] | AggregationQuery[dict[str, Any]]:
        """Creates a Mongo query.

        Parameters
        ----------
        model : type[T]
            Database model.
        start_index : int | None, optional
            Number of documents to skip, by default None.
        limit : int | None, optional
            Number of documents to limit, by default None.
        format_filter : FormatFilter | None, optional
            Expressions to filter documents, by default None.
        fetch_links : bool, optional
            Whether fetching links, by default False.
        projection_model : type[ProjectionModel] | None, optional.
            Projection model, by default None.
        sort : str | tuple[str, SortDirection] | list[tuple[str, SortDirection]] | None, optional
            Pattern or patterns to sort documents, by default None.

        Returns
        -------
        FindMany[T] | FindMany[ProjectionModel] | AggregationQuery[ProjectionModel] | AggregationQuery[dict[str, Any]]
            Prepared Mongo query.
        """

        # Recognize model fields from the model schema
        schema = model.schema()
        schema_properties = schema.get('properties')
        if schema_properties:
            self.__properties = schema_properties
        else:
            schema_definitions = schema.get('definitions')
            if schema_definitions and model.__name__ in schema_definitions:
                self.__properties = schema_definitions[model.__name__].get('properties')

        # Creates the query
        query = model.find(*args)

        # Filtering
        if format_filter:
            query = self.__filter.create(format_filter, query, True)

        # Skipping, limiting and sorting
        query = query.skip(start_index).limit(limit).sort(sort)

        # Aggregations
        aggregations = []

        if fetch_links:
            self.__get_fetch_links_aggregations(aggregations)
        self.__get_translation_aggregations(aggregations, model)
        self.__get_projection_aggregations(aggregations, model)

        if aggregations:
            aggregation_pipeline: list[dict[str, Any]] = construct_lookup_queries(query.document_model)
            aggregation_pipeline.append({'$match': query.get_filter_query()})
            sort_pipeline = {'$sort': {i[0]: i[1] for i in query.sort_expressions}}
            if sort_pipeline['$sort']:
                aggregation_pipeline.append(sort_pipeline)
            if query.skip_number != 0:
                aggregation_pipeline.append({'$skip': query.skip_number})
            if query.limit_number != 0:
                aggregation_pipeline.append({'$limit': query.limit_number})

            aggregation_pipeline.extend(aggregations)

            query = query.aggregate(aggregation_pipeline, projection_model=projection_model)
        else:
            query = query.project(projection_model)

        return query
