import inspect
import random
import re
import string
from collections.abc import Callable
from functools import wraps
from hashlib import sha256
from typing import Any, TypeVar, cast
from urllib.parse import urlparse

import xmltodict
from bs4 import BeautifulSoup, Tag
from lib.exceptions import AutomationError
from lib.interfaces import FormData

DecoratedClass = TypeVar('DecoratedClass', bound=object)


def split_authorization_header(auth_header_value: str) -> tuple[str, str]:
    """Split an Authorization header string into
    its scheme and credentials.

    Parameters
    ----------
    auth_header_value : str
        The full Authorization header string.

    Returns
    -------
    tuple[str, str]
        A tuple containing scheme and credentials.

    """
    parts = auth_header_value.split(maxsplit=1)
    if len(parts) == 2:
        scheme, cred = parts
    elif len(parts) == 1:
        scheme, cred = parts[0], ''
    else:
        scheme, cred = '', ''
    return scheme.lower().strip(), cred.strip()


def password_generator(
    length: int = 16,
    digits: int = 4,
    uppercase_characters: int = 3,
    special_characters: int = 2,
) -> str:
    """Generates a random password.

    Parameters
    ----------
    length : int, optional
        Password length, by default 16.
    digits : int, optional
        Number of digits, by default 4.
    uppercase_characters : int, optional
        Number of uppercase characters, by default 3.
    special_characters : int, optional
        Number of special characters, by default 2.

    Returns
    -------
    str
        Generated password.
    """

    password = ''
    lowercase_characters = (
        length - digits - uppercase_characters - special_characters
    )
    for _ in range(uppercase_characters):
        password += random.choice(string.ascii_uppercase)
    for _ in range(lowercase_characters):
        password += random.choice(string.ascii_lowercase)
    for _ in range(digits):
        password += random.choice(string.digits)
    for _ in range(special_characters):
        password += random.choice('#$%&*.-')
    return password


def retry(
    times: int,
    exceptions: tuple[type[Exception]] = (Exception,),
    raise_exceptions: tuple[type[Exception]] | None = None,
    attempt_callback: Callable | None = None,
) -> Callable:
    """Retries the wrapped function `times` times
    if the exceptions listed in `exceptions` are thrown.

    Parameters
    ----------
    times : int
        The number of times to repeat the wrapped function.
    exceptions : typing.Iterable[type[Exception]], optional
        Lists of exceptions that trigger a retry attempt, by default (Exception,).
    raise_exceptions : typing.Iterable[type[Exception]] | None, optional
        Lists of exceptions to raise instead of triggering a retry attempt, by default None.
    attempt_callback : Callable | None, optional
        Callback function to run between every attempt, by default None.

    Returns
    -------
    Callable
        Decorated function.
    """

    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            while attempt < times:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if raise_exceptions and isinstance(
                        e, tuple(raise_exceptions)
                    ):
                        raise e
                    if attempt_callback:
                        attempt_callback()
                    attempt += 1
            return func(*args, **kwargs)

        return wrapper

    return decorator


def get_html_soup(text: str) -> BeautifulSoup:
    """Creates a BeautifulSoup instance to parse
    the passed `text` using the HTML parser.

    Parameters
    ----------
    text : str
        Text to be parsed with the HTML parser.

    Returns
    -------
    BeautifulSoup
        BeautifulSoup instance with the parsed text.
    """

    return BeautifulSoup(text, 'html.parser')


def get_xml_soup(text: str) -> BeautifulSoup:
    """Creates a BeautifulSoup instance to parse
    the passed `text` using the XML parser.

    Parameters
    ----------
    text : str
        Text to be parsed with the XML parser.

    Returns
    -------
    BeautifulSoup
        BeautifulSoup instance with the parsed text.
    """

    return BeautifulSoup(text, 'xml')


def get_form_data(
    soup_or_form: BeautifulSoup | Tag,
    error_message: str = 'No se encontró ninguna etiqueta form',
) -> FormData:
    """Converts a BeautifulSoup form tag to a
    `FormData` object.

    A `FormData` object contains the method, action (url)
    and the inputs (data) of the form as shown below::

        class FormData:
            method: str
            action: str
            data: dict[str, Any]

    Parameters
    ----------
    soup_or_form : BeautifulSoup | Tag
        BeautifulSoup instance containing the form tag
        or the form tag itself.
    error_message : str, optional
        Message of the exception to raise if the `soup_or_form`
        parameter is a BeautifulSoup instance and no form is found,
        by default 'No se encontró ninguna etiqueta form'.

    Returns
    -------
    FormData
        FormData object containing the form's method, action and data.

    Raises
    ------
    AutomationError
        No form was found in the BeautifulSoup instance.
    """

    if isinstance(soup_or_form, BeautifulSoup):
        form = soup_or_form.form
        if not form:
            raise AutomationError(error_message, detail=soup_or_form.text)
    else:
        form = soup_or_form

    data = {}
    for _input in form.find_all('input'):
        if not _input.get('name'):
            continue
        data[_input['name']] = _input.get('value')
    for _select in form.find_all('select'):
        if not _select.get('name'):
            continue
        selected_option = _select.find('option', {'selected': True})
        data[_select['name']] = (
            selected_option.get('value') if selected_option else None
        )

    return FormData(
        method=cast(str, form.get('method', '')),
        action=cast(str, form.get('action', '')),
        data=data,
        form_id=form.attrs.get('id', ''),
        form_name=form.attrs.get('name', ''),
    )


def strip_text(text: str) -> str:
    """Returns a copy of the `text` argument
    with multiple spaces and line breaks removed.

    Parameters
    ----------
    text : str
        Text to strip.

    Returns
    -------
    str
        Stripped text.
    """

    text = re.sub('\n', ' ', text)
    text = re.sub(' +', ' ', text)
    return text.strip()


def extract_digits(text: str) -> str:
    """Extracts digits from `text`.

    Parameters
    ----------
    text : str
        Source.

    Returns
    -------
    str
        Found digits.
    """

    return re.sub(r'\D', '', text)


def extract_trailing_letters(text: str) -> str:
    """Extracts trailing letters from `text`.

    Parameters
    ----------
    text : str
        Source.

    Returns
    -------
    str
        Found trailing letters.
    """

    matches = re.search(r'^[a-zA-Z]+', text)
    return matches.group(0) if matches else ''


def search_group(pattern: str, text: str) -> str | None:
    """Scan through string looking for a match
    to the `pattern`, returning the capturing group,
    or None if no match was found.

    Parameters
    ----------
    pattern : str
        Pattern.
    text : str
        Text.

    Returns
    -------
    str | None
        Capturing group.
    """

    match = re.search(pattern, text, re.IGNORECASE)
    if not match:
        return None
    return match.group(1)


def sha256_from_string(string: str) -> str:
    """Generate a SHA256 from a string.

    Parameters
    ----------
    string : str
        String.

    Returns
    -------
    str
        Hash.
    """

    hash_object = sha256()
    hash_object.update(string.encode('utf-8'))
    return hash_object.hexdigest()


def extract_query(url: str) -> str:
    """Extracts the query from the passed `url`.

    Parameters
    ----------
    url : str
        URL.

    Returns
    -------
    str
        URL encoded query.
    """

    return urlparse(url).query


def xml_to_dict(xml: str) -> dict[str, Any]:
    """Parse XML into a dict.

    Parameters
    ----------
    xml : str
        XML text.

    Returns
    -------
    dict[str, Any]
        Parsed data.

    """
    return xmltodict.parse(xml.strip(), xml_attribs=False)


def filter_dict_list_by_field(
    obj_list: list[dict[str, Any]],
    key: str,
    value: Any,
) -> Any:
    """Extract one dict in a list filtering by key and value.

    Parameters
    ----------
    obj_list : list[dict[str, Any]]
        List of dicts.
    key : str, optional
        Name of the field to extract, by default 'id'.
    value : Any
        Value of the field to extract.

    Returns
    -------
    dict[str, Any]
        Extracted field.

    """
    field = next(
        filter(lambda field: field.get(key, '') == value, obj_list),
        {},
    )
    return field


def get_nested(source: dict[str, Any], nested_key: str) -> dict[str, Any]:
    """Get a nested dict or return an empty dict.

    Parameters
    ----------
    source : dict[str, Any]
        Source dict.
    nested_key : str
        Nested dict key.

    Returns
    -------
    dict[str, Any]
        Nested dict.

    """
    nested = source.get(nested_key, {})
    return nested or {}


def decorate_all_methods(
    decorator: Callable,
    prefix: str = '',
    suffix: str = '',
) -> Callable[[type[DecoratedClass]], type[DecoratedClass]]:
    def _class_wrapper(cls) -> type[DecoratedClass]:
        for name, method in cls.__dict__.items():
            if (
                inspect.isfunction(method)
                and name.startswith(prefix)
                and name.endswith(suffix)
            ):
                setattr(cls, name, decorator(method))
        return cls

    return _class_wrapper
