from datetime import datetime
from typing import Any

import orjson
from beanie import PydanticObjectId
from beanie.odm.fields import Link

from lib.base_serializer import BaseSerializer


class BaseDto(BaseSerializer):
    """Base class for DTOs."""

    pass


class BaseRequestDto(BaseDto):
    """DTO for requests."""

    createdBy: Any | None = None
    updatedBy: Any | None = None


class BaseResponseDto(BaseDto):
    """DTO for responses."""

    def to_response(self) -> dict[str, object]:
        """Serialize DTO to dict.

        Returns
        -------
        dict[str, object]
            Serialized DTO.

        """
        dict_response = self.to_dict()
        for key, value in dict_response.items():
            if isinstance(value, Link):
                dict_response[key] = str(value.ref.id)
            if isinstance(value, (PydanticObjectId, datetime)):
                dict_response[key] = str(value)

        return dict_response

    def to_json(self) -> bytes:
        """Serialize to JSON using orjson.

        Returns
        -------
        bytes
            JSON content.

        """
        return orjson.dumps(self.to_response())
