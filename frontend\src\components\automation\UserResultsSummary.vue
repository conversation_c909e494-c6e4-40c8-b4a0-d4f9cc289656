<template>
  <div
    :class="`row justify-between items-center q-mx-sm q-mt-md q-py-md q-px-lg rounded-borders ${$q.dark.isActive ? 'bg-grey-10' : 'bg-cyan-1'}`">
    <div class="column">
      <p class="q-pa-none q-ma-none">
        <span class="text-bold text-primary">{{ data.startDateLabel }}: </span>
        {{ dateParse(user.updatedAt) }}
      </p>
      <p class="q-pa-none q-ma-none">
        <span class="text-bold text-primary">{{ data.endDateLabel }}: </span>
        {{ getEndDate() }}
      </p>
      <p class="q-pa-none q-ma-none">
        <span class="text-bold text-primary">{{ data.timeLabel }}: </span>
        {{ secondsToSchedule(user.time) }}
      </p>
      <p v-if="user.catalogNumber" class="q-pa-none q-ma-none">
        <span class="text-bold text-primary">{{ t('catalogNumber') }}: </span>
        {{ user.catalogNumber }}
      </p>
      <p class="q-pa-none q-ma-none">
        <span class="text-bold text-primary">{{ t('applicationsCount') }}: </span>
        {{ user.data.length }}
      </p>
      <p v-if="user.errorCount" class="q-pa-none q-ma-none">
        <span class="text-bold text-negative">{{ t('applicationsWithError') }}: </span>
        {{ user.errorCount }}
      </p>
      <p v-if="user.warningCount" class="q-pa-none q-ma-none">
        <span class="text-bold text-orange-7">{{ t('applicationsWithWarning') }}: </span>
        {{ user.warningCount }}
      </p>
    </div>
    <q-btn v-if="hideDownloadButton" :label="t('export')" color="positive" class="q-mr-sm"
      :disable="disableDownloadButton" @click="emit('downloadUserResults')" />
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useQuasar } from 'quasar';
import { reactive } from 'vue';
import { useI18n } from 'vue-i18n';

import { dateParse, secondsToSchedule } from 'src/lib/tools';
import type { ConsultedUserResponse } from 'src/models/consultedUser';
import { Action } from 'src/models/transaction';
//#endregion

//#region types
interface Props {
  action: Action;
  user: ConsultedUserResponse;
  hideDownloadButton?: boolean;
  disableDownloadButton?: boolean;
}

interface Emits {
  (e: 'downloadUserResults'): void;
}
//#endregion
const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const $q = useQuasar();

const { t } = useI18n();

const data = reactive({
  startDateLabel: props.action === Action.consult ? t('queryStartDate') : t('userDeletionStartDate'),
  endDateLabel: props.action === Action.consult ? t('queryEndDate') : t('userDeletionEndDate'),
  timeLabel: props.action === Action.consult ? t('queriesTime') : t('userDeletionTime'),
});

function getEndDate(): string {
  const endDate = new Date(props.user.updatedAt);
  endDate.setSeconds(endDate.getSeconds() + props.user.time);
  return dateParse(endDate);
}
</script>
