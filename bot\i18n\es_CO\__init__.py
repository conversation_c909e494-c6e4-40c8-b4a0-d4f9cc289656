es_CO = {
    #region common
    'an_error_has_occurred': 'Ha ocurrido un error: {error}',
    'minimum_length_must_be': 'La longitud mínima requerida es de {length} caracteres.',
    'export_not_enabled': 'La exportación de datos no está habilitada.',
    'could_not_write_file': 'El archivo no se pudo escribir correctamente.',
    'file_not_found': 'El archivo "{filename}" no fue encontrado.',
    'no_data_available': 'No hay datos disponibles.',
    'could_not_write_data': 'No se pudo realizar la escritura de datos.',
    'do_not_have_authorization': 'No tiene autorización para ejecutar esta acción.',
    'mail_could_not_be_sent': 'No se pudo enviar el mensaje, por favor intenta de nuevo más tarde.',
    'user': 'Usuario',
    'username': 'Nombre de usuario',
    'fullname': 'Nombre completo',
    'full_name': 'Nombre completo',
    'document': 'Número de documento',
    'email': 'Correo electrónico',
    'time': 'Tiempo',
    'done': 'Hecho',
    'error': 'Error',
    'ignored': 'Omitido',
    'warning': 'Advertencia',
    'status': 'Estado',
    'platform': 'Plataforma',
    'message': 'Mensaje',
    'updated_at': 'Fecha de actualización',
    'created_at': 'Fecha de creación',
    'updated_by': 'Actualizado por',
    'created_by': 'Creado por',
    'consulted_by': 'Consultado por',
    'removed_by': 'Retirado por',
    'catalog_number': 'Número de catálogo',
    'action': 'Acción',
    'consult': 'Consultar',
    'remove': 'Retirar',
    'correct': 'Correcto',
    'result': 'Resultado',
    'active': 'Activo',
    'inactive': 'Inactivo',
    'frozen': 'Congelado',
    'branch': 'Sede',
    'position': 'Cargo',
    'name': 'Nombre',
    'role': 'Rol',
    'sam_account_name': 'Usuario',
    'user_principal_name': 'Usuario de Azure',
    'first_name': 'Nombres',
    'last_name': 'Apellidos',
    'mail': 'Correo electrónico',
    'member_of': 'Grupos',
    'groups': 'Grupos',
    'locked': 'Bloqueado',
    'enabled': 'Habilitado',
    'roles': 'Roles',
    'authorities': 'Aseguradores de autoridad',
    'profile': 'Perfil',
    'profiles': 'Perfiles',
    'company': 'Compañía',
    'id': 'ID del usuario',
    'license': 'Licencia',
    'licenses': 'Licencias',
    'removed_licenses': 'Licencias retiradas',
    'channels': 'Canales',
    'updated': 'Actualizado',
    'discharge_date': 'Fecha de alta',
    'leaving_date': 'Fecha de baja',
    'cas': 'CAS',
    'state': 'Estado',
    'applications': 'Aplicaciones',
    'product': 'Producto',
    'code': 'Código',
    'description': 'Descripción',
    'category': 'Categoría',
    'permission_codes': 'Códigos de permiso',
    'branches': 'Ramos',
    'offices': 'Oficinas',
    'user_type': 'Tipo de usuario',
    'activation_date': 'Fecha de activación',
    'expiration_date': 'Fecha de retiro',
    'boss_document': 'Documento del jefe',
    'employee_status': 'Estado del empleado',
    'payroll_code': 'Código de nómina',
    'absenteeism_status': 'Estado de ausentismo',
    'repository': 'Repositorio',
    'removed_groups': 'Grupos retirados',
    'removed_role': 'Rol retirado',
    'removed_roles': 'Roles retirados',
    'unremoved_roles': 'Roles no retirados',
    #endregion

    #region auth
    'invalid_credentials': 'Usuario o contraseña no válidos.',
    'invalid_token': 'Token no válido.',
    'expired_token': 'La sesión ha expirado, por favor inicie sesión nuevamente.',
    #endregion

    #region validators
    'invalid_cell_phone': 'Número de celular inválido.',
    'invalid_value': 'Valor inválido.',
    'invalid_email': 'Email inválido.',
    'must_be_greater_equals_than': 'El valor debe ser mayor o igual que {compare_value}.',
    'must_be_greater_than': 'El valor debe ser mayor que {compare_value}.',
    'is_not_numeric_value': 'No es un valor numérico.',
    'id_number_invalid': 'Número de identificación inválido, debe tener de 6 a 10 dígitos.',
    'invalid_option': 'Opción no valida.',
    'must_be_lower_equals_than': 'El valor debe ser menor o igual que {compare_value}.',
    'must_be_lower_than': 'El valor debe ser menor que {compare_value}.',
    'must_be_max_length': 'El valor debe tener máximo {max_length_value} caracteres.',
    'must_be_min_length': 'El valor debe tener mínimo {min_length_value} caracteres.',
    'incorrect_field': 'El campo {field} es incorrecto.',
    'invalid_password': 'La contraseña no es válida, debe tener al menos {length} caracteres incluyendo mayúsculas, minúsculas, números y caracteres especiales, por ejemplo: !#$%&.*+/=?^_`|~-]',
    'invalid_phone': 'Número de teléfono inválido.',
    'must_be_between_length': 'El valor debe tener entre {start_number} y {end_number} caracteres.',
    'must_be_between': 'El valor debe estar entre {start_number} y {end_number}.',
    'required_field': 'Este campo es obligatorio.',
    'not_exceed_number': 'El número de preguntas de recuperación no puede superar el número de preguntas de registro.',
    #endregion

    #region filter
    'date_restricted': 'La expresión de fecha debe ser una de las siguientes ["eq", "lte", "gte", "lt", "gt"].',
    #endregion

    #region active directory
    'ad_data_required_advise': 'Debido a esto, no se pudo realizar la consulta en algunas aplicaciones como IPSA, SOAT, Office, entre otras, las cuales dependen de datos del usuario que se debían obtener desde el Directorio Activo (nombre completo, email, número de documento, etc.).',
    #endregion

    #region logs
    'transaction': 'Transacción',
    'module_name': 'Módulo',
    'module_func_args_spec': 'Especificación de la función del módulo',
    'module_func_args': 'Argumentos pasados a la función del módulo',
    'application': 'Clave de la aplicación',
    'application_name': 'Nombre de la aplicación',
    'data': 'Datos',
    'detail': 'Detalle',
    'start_time': 'Tiempo de inicio',
    'end_time': 'Tiempo de fin',
    'time_diff': 'Diferencia de tiempo',
    'user_not_found': 'Usuario no encontrado',
    #endregion

    #region applications
    'activeDirectory': 'Active Directory',
    'office': 'Office 365',
    'AgendaWebModule': 'Agenda Web',
    'AribaModule': 'Ariba',
    'BandejaEscalamientoModule': 'Bandeja de Escalamiento',
    'BeyondHealthModule': 'Beyond Health',
    'BillingCenterModule': 'Billing Center',
    'CAServiceDeskModule': 'CA Service Desk',
    'CaseTrackingModule': 'Case Tracking',
    'ClaimCenterModule': 'Claim Center',
    'ConfluenceModule': 'Confluence',
    'ContactManagerModule': 'Contact Manager',
    'ConveniosModule': 'Convenios',
    'EventosAdversosModule': 'Eventos Adversos',
    'HealthCloudModule': 'Health Cloud',
    'IDMModule': 'IDM',
    'IntegradorModule': 'Integrador',
    'IPSAModule': 'IPSA',
    'OfficeModule': 'Office 365',
    'OHIModule': 'OHI',
    'OIPAModule': 'OIPA',
    'PolicyCenterModule': 'Policy Center',
    'PorfinModule': 'Porfin',
    'SalesforceModule': 'Salesforce',
    'SaludWebModule': 'Salud Web',
    'SEEEModule': 'SEEE',
    'SEUSModule': 'SEUS 4',
    'SOATModule': 'SOAT',
    'STARModule': 'STAR',
    'TablaTercerosModule': 'Tabla de Terceros',
    'ViafirmaModule': 'Viafirma Inbox',
    #endregion
}
