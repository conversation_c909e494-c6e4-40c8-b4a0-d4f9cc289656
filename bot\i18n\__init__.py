from typing import Any, cast

from lib.base_injectable import BaseInjectable
from lib.tools import get_nested

from .es_CO import es_CO


class I18N(BaseInjectable):
    language: str | None

    languages = {
        'es_CO': es_CO,
    }

    def __call__(self, key: str, format_values: dict[str, Any] | None = None) -> str:
        if not hasattr(self, 'language') or self.language is None:
            self.language = 'es_CO'

        language_dict = self.languages.get(cast(str, self.language))
        if language_dict is None:
            language_dict = get_nested(self.languages, 'es_CO')

        message = language_dict.get(key)
        if message is None:
            return f'[[{key}]]'

        if format_values:
            return message.format_map(format_values)

        return message

    def get_dictionary(self) -> dict[str, str] | None:
        if not hasattr(self, 'language') or self.language is None:
            self.language = 'es_CO'
        return self.languages.get(cast(str, self.language))
