from lib.base_error import BaseError


class AutomationError(BaseError):
    detail: str | None
    """Error response's text or error's raw message."""

    def __init__(
        self,
        message: str = 'Automation error.',
        code: int = 400,
        detail: str | None = None,
    ):
        super().__init__(message, code)
        self.detail = detail

    def get_full_message(self) -> str:
        """Gets the full error message.

        Returns
        -------
        str
            Full error message.
        """
        msg = (
            f'{str(self)} (DETAIL: {self.detail})'
            if self.detail
            else str(self)
        )
        return f'[{self.code}] {msg}'


class DisconnectedError(BaseError):
    def __init__(self, message: str = 'Disconnected.', code: int = 499):
        super().__init__(message, code)


class BadRequestError(BaseError):
    def __init__(self, message: str = 'Bad request.', code: int = 400):
        super().__init__(message, code)


class NotFoundError(BaseError):
    def __init__(self, message: str = 'Not found.', code: int = 404):
        super().__init__(message, code)


class ForbiddenError(BaseError):
    def __init__(self, message: str = 'Forbidden.', code: int = 403):
        super().__init__(message, code)


class UnauthorizedError(BaseError):
    def __init__(self, message: str = 'Unauthorized.', code: int = 401):
        super().__init__(message, code)


class BadQueryError(BaseError):
    def __init__(self, message: str = 'Bad query.', code: int = 400):
        super().__init__(message, code)
