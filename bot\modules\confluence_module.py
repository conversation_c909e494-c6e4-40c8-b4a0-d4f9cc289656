import json
from typing import Any

from dtos.automation_dto import ConfluenceResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import retry

SEARCH_USERS_URL = 'https://api.atlassian.com/admin/v1/orgs/%s/users/search'


class ConfluenceModule(BaseModule):
    """Provide a function to consult the status and
    group memberships of an user on Confluence.
    """

    def __init__(self) -> None:
        self.headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {config.CONFLUENCE_API_TOKEN!s}',
        }

    def _fetch_users_by_fullname(self, fullname: str) -> list[dict[str, Any]]:
        """Fetch users by a fullname.

        Parameters
        ----------
        fullname : str
            Fullname of the user to consult.

        Returns
        -------
        list[dict[str, Any]]
            Found users.

        Raises
        ------
        AutomationError
            If user could not be consulted.
        AutomationError
            If result of the consulted user could not be obtained.

        """
        data = {
            'namesOrNicknames': {'eq': [fullname]},
            'expand': ['NAME', 'EMAIL', 'GROUPS'],
        }
        url = SEARCH_USERS_URL % config.CONFLUENCE_ORG_ID
        response = self.session.post(url=url, headers=self.headers, json=data)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario en Confluence.',
                detail=response.text,
            )
        return response.json()['data']

    def _get_one_user(self, fullname: str) -> dict[str, Any]:
        """Get one user by its fullname.

        Parameters
        ----------
        fullname : str
            Fullname of the user to consult.

        Returns
        -------
        dict[str, Any]
            User data.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If multiple users found.

        """
        users = self._fetch_users_by_fullname(fullname)
        if not users:
            raise NotFoundError()

        if len(users) > 1:
            raise AutomationError(
                f'Se encontraron múltiples usuarios con nombre "{fullname}".',
                detail=json.dumps(users),
            )

        return users[0]

    def _create_user_data_dto(
        self, user: dict[str, Any]
    ) -> ConfluenceResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user : dict[str, Any]
            User data.

        Returns
        -------
        ConfluenceResponseDto
            Requested user data.

        """
        active = user.get('statusInUserbase', False)
        groups = [group.get('name') for group in user.get('groups', [])]
        return ConfluenceResponseDto(active=active, groups=groups)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, fullname: str) -> ConfluenceResponseDto:
        """Consult the status and group memberships
        of an user on Confluence.

        Parameters
        ----------
        fullname : str
            Fullname of the user to consult.

        Returns
        -------
        ConfluenceResponseDto
            Data of the user.

        """
        try:
            self.create_session()
            user = self._get_one_user(fullname)
            return self._create_user_data_dto(user)
        finally:
            self.close_session()
