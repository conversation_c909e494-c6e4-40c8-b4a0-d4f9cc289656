import asyncio
import inspect
import itertools
from collections.abc import Callable
from dataclasses import dataclass
from functools import wraps
from typing import Any

from starlette.concurrency import run_in_threadpool

from lib import config
from lib.singleton import Singleton


@dataclass
class Task:
    """Task to be executed by the task queue manager."""

    id_: int
    func: Callable[..., Any]
    args: tuple
    kwargs: dict
    future: asyncio.Future
    timeout: float | None = None

    async def execute(self) -> Any:
        """Execute the task.

        Returns
        -------
        Any
            Result of the task execution.

        """
        if inspect.iscoroutinefunction(self.func):
            return await self.func(*self.args, **self.kwargs)
        else:
            return await run_in_threadpool(self.func, *self.args, **self.kwargs)


class Queue(asyncio.Queue):
    def __init__(self, name: str, maxsize: int = 0) -> None:
        """Initialize the queue.

        Parameters
        ----------
        name : str
            Queue name.
        maxsize : int, optional
            Maximum size of the queue, by default 0.

        """
        super().__init__(maxsize)
        self.name = name

    @property
    def pending(self) -> int:
        """Number of pending tasks in the queue."""
        return self.qsize()

    async def get(self) -> Task:
        return await super().get()

    async def put(self, item: Task) -> None:
        return await super().put(item)


class TaskQueueManager(Singleton):
    """Task queue manager."""

    def __init__(self, default_timeout: float = 10.0) -> None:
        """Initialize the task queue manager.

        Parameters
        ----------
        default_timeout : float, optional
            Default timeout for the queues, by default 10.0.

        """
        self.__queues = {}
        self._default_timeout = default_timeout
        self._task_counter = itertools.count(1)

    def get_queue(self, name: str) -> Queue:
        """Get a queue by name or create it if it does not exist.

        Parameters
        ----------
        name : str
            Queue name.

        Returns
        -------
        Queue
            Queue.

        """
        if name not in self.__queues:
            queue = Queue(name)
            asyncio.create_task(self._worker(queue))
            self.__queues[name] = queue
        return self.__queues[name]

    async def _worker(self, queue: Queue) -> None:
        """Worker that executes the tasks in the queue.

        Parameters
        ----------
        queue : Queue
            Queue.

        """
        name = queue.name
        while True:
            task = await queue.get()
            try:
                result = await asyncio.wait_for(
                    task.execute(),
                    timeout=task.timeout or self._default_timeout,
                )
                task.future.set_result(result)
            except asyncio.TimeoutError:
                task.future.set_exception(
                    asyncio.TimeoutError(f'Tarea {task.id_} en {name} expiró')
                )
            except Exception as e:
                task.future.set_exception(e)
            finally:
                queue.task_done()

    def _get_new_task_id(self) -> int:
        """Get a new task id."""
        return next(self._task_counter)

    def enqueue(self, queue_name: str, timeout: float | None = None):
        """Enqueue a task in the specified queue.

        Parameters
        ----------
        queue_name : str
            Queue name.
        timeout : float | None, optional
            Timeout for the task, by default None.

        Returns
        -------
        Callable
            Decorated function.

        """

        def decorator(func: Callable[..., Any]):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                loop = asyncio.get_running_loop()
                future = loop.create_future()
                queue = self.get_queue(queue_name)
                task_id = self._get_new_task_id()
                task = Task(task_id, func, args, kwargs, future, timeout)
                await queue.put(task)
                return await future

            return wrapper

        return decorator


manager = TaskQueueManager(config.TASK_QUEUE_MANAGER_DEFAULT_TIMEOUT)
