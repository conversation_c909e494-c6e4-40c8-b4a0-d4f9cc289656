import re
import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class PhoneValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, include_areaCode: bool, message: str | None = None) -> None:
        super().__init__(value)
        self.__include_areaCode = include_areaCode
        self.message = message if message else 'invalid_phone'

    async def validate(self) -> bool:
        value = self.value()
        if self.__include_areaCode:
            validate_fields: str = r'^[0-9]{3} [0-9]{7}$'
            self.is_valid = re.search(validate_fields, value) is not None if value else True
            return self.is_valid

        validate_fields: str = r'^[0-9]{7}$'
        self.is_valid = re.search(validate_fields, value) is not None if value else True
        return self.is_valid
