<template>
  <div class="absolute-center text-center" style="width: 50%;">
    <div class="text-center">
      <q-spinner-ios color="primary" size="50px" />
      <p class="q-mt-md" style="font-size: 14px">
        {{ props.message ? props.message : t('loading') }}
      </p>
    </div>
    <q-linear-progress stripe rounded size="25px" :value="props.progress" color="primary">
      <div class="absolute-full flex flex-center">
        <q-badge color="white" text-color="primary" :label="getPercentage(props.progress)" />
      </div>
    </q-linear-progress>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useI18n } from 'vue-i18n';

import { getPercentage } from 'src/lib/tools';
//#endregion

//#region types
interface ProgressProps {
  progress: number;
  message?: string;
}
//#endregion
const props = defineProps<ProgressProps>();

const { t } = useI18n();
</script>
