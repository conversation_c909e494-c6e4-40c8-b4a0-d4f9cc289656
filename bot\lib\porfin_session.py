import json
import math
import random
import re
from dataclasses import dataclass
from urllib.parse import quote

import xmltodict
from requests import Session
from requests.cookies import RequestsCookieJar

from lib import config
from lib.base_injectable import BaseInjectable
from lib.exceptions import AutomationError
from lib.http.sessions import TLSSession
from lib.logger import logger


@dataclass
class PorfinCredentials:
    username: str
    password: str


@dataclass
class PorfinForm:
    form_id: str
    hc1: str
    hc2: str
    hc3: str


class PorfinSession(BaseInjectable):
    """Perform the authentication on Porfin platform."""

    def __init__(self):
        self.__session_id = None
        self.__forms_params = None
        self.last_form = None
        self.__auth_cookies = RequestsCookieJar()
        self.__base_url = f'https://{config.PORFIN_DOMAIN}/'
        self.__index_url = f'{self.__base_url}/AlfaGL/index.html'
        self.__menu_url = f'{self.__base_url}/AlfaGL/alfa.menu'
        self.__form_url = f'{self.__base_url}/seguro_sura/FrameworkServlet'

    def get_credentials(self) -> PorfinCredentials:
        """Get the credentials of the connection user.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        PorfinCredentials
            Credentials of the connection user.

        """
        return PorfinCredentials(
            username=str(config.PORFIN_USER),
            password=str(config.PORFIN_PASSWORD),
        )

    def authenticate(self) -> Session:
        """Perform the login in the platform."""
        self.close_session()  # Close session if exists
        self.create_session()

        self.set_cookies()
        if self.authenticated and self.check_session_alive():
            return self.session

        self.clear_cookies()
        self.login()
        self.__session_id = self.fetch_forms_params_and_session_id()
        self.save_cookies()

        return self.session

    @property
    def authenticated(self) -> bool:
        """Check if user is authenticated.

        User is authenticated if:
            - It has a session id.
            - It has the forms params.
        """
        return (
            self.__session_id is not None and self.__forms_params is not None
        )

    def save_cookies(self) -> None:
        """Save the current cookies."""
        self.__auth_cookies.clear()
        self.__auth_cookies.update(self.session.cookies)

    def set_cookies(self) -> None:
        """Set the existing cookies to the current session."""
        self.session.cookies.update(self.__auth_cookies)

    def clear_cookies(self) -> None:
        """Clear the cookies."""
        self.session.cookies.clear()
        self.__auth_cookies.clear()

    def random_coding(self) -> str:
        """Generate a random number between 0 and 25 and get
        a Unicode string.

        NOTE: This method is extracted and translated from the source code
        of the platform.

        Some of the instructions of this method could not be understood,
        but all are required to perform the login.

        The url of the JavaScript code is
        https://<domain>/AlfaGL/js/login.js.

        Returns
        -------
        str
            Random Unicode string.

        """
        random_number = math.ceil(random.random() * 25)
        return chr(65 + random_number)

    def encrypt_value_to_unicode(self, value: str) -> str:
        """Encrypt a string value using random Unicode strings.

        NOTE: This method is extracted and translated from the source code
        of the platform.

        Some of the instructions of this method could not be understood,
        but all are required to perform the login.

        The url of the JavaScript code is
        https://<domain>/AlfaGL/js/login.js.

        Parameters
        ----------
        value : str
            Value to encrypt.

        Returns
        -------
        str
            Encrypted value.

        """
        out = ''
        p = ord(value[0])
        for character in value:
            out = out + str(ord(character) * p) + '!'
            p = ord(character)
        return (
            out
            + str(ord(self.random_coding()) + ord(self.random_coding()))
            + '!'
            + str(ord(self.random_coding()) + ord(self.random_coding()))
            + '!'
            + str(ord(self.random_coding()) + ord(self.random_coding()))
        )

    def encode_uri_component(self, text: str) -> str:
        """Quote a string to its URI representation.

        NOTE: This method is extracted and translated from the source code
        of the platform.

        Some of the instructions of this method could not be understood,
        but all are required to perform the login.

        The url of the JavaScript code is
        https://<domain>/AlfaGL/js/login.js.

        Parameters
        ----------
        text : str
            Text to quote.

        Returns
        -------
        str
            Quoted text.

        """
        return quote(text, safe="~()*!'")

    def encrypt_credencial(self, credential: str) -> str:
        """Encrypt a credential.

        NOTE: This method is extracted and translated from the source code
        of the platform.

        Some of the instructions of this method could not be understood,
        but all are required to perform the login.

        The url of the JavaScript code is
        https://<domain>/AlfaGL/js/login.js.

        Parameters
        ----------
        credential : str
            Credential to encrypt.

        Returns
        -------
        str
            Encrypted credential.

        """
        return self.encode_uri_component(
            self.encrypt_value_to_unicode(credential)
        )

    def login(self) -> None:
        """Perform the login in the platform.

        The login method implements a custom encryption method
        on credentials sent as "x1" (username) and "x2" (password).

        NOTE: The encryption methods are extracted and translated
        from the source code of the platform.

        Some of the instructions of this method could not be understood,
        but all are required to perform the login.

        The url of the JavaScript code is
        https://<domain>/AlfaGL/js/login.js.

        Raises
        ------
        AutomationError
            If credential could not be encoded.
        AutomationError
            If login could not be performed.
        AutomationError
            If an unknown error occurred.

        """
        url = f'{self.__base_url}/AlfaGL/login.html'
        self.session.get(url)

        try:
            credentials = self.get_credentials()
            data = {
                'x1': self.encrypt_credencial(credentials.username),
                'x2': self.encrypt_credencial(credentials.password),
            }
        except Exception as e:
            raise AutomationError(
                'No se pudo codificar las credenciales del login de Porfin.',
                detail=str(e),
            )

        payload = json.dumps(data, ensure_ascii=False)
        url = f'{self.__base_url}/AlfaGL/UsernameValidator'
        response = self.session.post(url, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo iniciar sesión en Porfin.', detail=response.text
            )

        # If there is text in the response content,
        # the text would be an error message,
        # so it means that an error has occurred
        response_text = response.text.strip()
        if response_text.upper() != 'OK':
            raise AutomationError(
                'Ha ocurrido un error en el inicio de sesión de Porfin.',
                detail=response_text,
            )

    def check_session_alive(self) -> bool:
        """Check if the session is still alive.

        It means if the value of the "estado" response field
        is equal to the "OK" literal.

        Parameters
        ----------
        session_id : str
            Session id.

        Returns
        -------
        bool
            Whether session is alive.

        Raises
        ------
        AutomationError
            If session could not be checked.

        """
        if self.__session_id is None:
            return False

        url = f'{self.__base_url}/AlfaGL/SessionLive?session_id={self.__session_id}'
        response = self.session.get(url)
        if not response.ok:
            raise AutomationError(
                'No se pudo validar la sesión en Porfin.', detail=response.text
            )
        session_status = response.json()
        return session_status.get('estado') == 'OK'

    def fetch_forms_params_and_session_id(self) -> str:
        """Fetch the menu containing the session id
        and the forms params.

        The forms params corresponds to the params of the
        first FormParametersServlet request to get the
        form to consult/update a user on Porfin.

        Forms params look like:
        aplicacion=PFSEGURO#modo=JV#conexion=PDNFIN#modulo=PPL_SEG_053...

        Returns
        -------
        str
            Session id.

        Raises
        ------
        AutomationError
            If session id could not be found/obtained.

        """
        url = self.__menu_url
        response = self.session.post(url)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el id de la sesión en Porfin.',
                detail=response.text,
            )
        menu = response.json()
        session_id = menu.get('id', '')
        if not session_id:
            raise AutomationError(
                'No se encontró el id de la sesión en Porfin.',
                detail=response.text,
            )
        try:
            forms_url = menu['tree'][0]['nodes'][0]['nodes'][0]['nodes'][1][
                'nodes'
            ][1]['url']
            self.__forms_params = forms_url.split('?', 1)[1].replace('&', '#')
        except Exception as e:
            raise AutomationError(
                'No se pudo obtener los parámetros de los formularios en Porfin.',
                detail=str(e),
            )
        return session_id

    def open_form(self) -> str:
        """Open a new Porfin's form.

        Returns
        -------
        str
            Form's id.

        Raises
        ------
        AutomationError
            If form could not be opened.

        """
        url = f'{self.__base_url}/AlfaGL/OpenForm'
        response = self.session.get(url)
        if not response.ok:
            raise AutomationError(
                'No se pudo abrir el formulario en Porfin.',
                detail=response.text,
            )
        return response.text

    def form_parameters(self, form_id: str) -> str:
        """Initialize a form with the required configuration fields
        such as application's name, module's name and session's id.

        Parameters
        ----------
        form_id : str
            Id of the form.

        Returns
        -------
        str
            Id of the initialized form.

        Raises
        ------
        AutomationError
            If form could not be initialized.

        """
        if self.__forms_params is None:
            raise AutomationError(
                'No se han obtenido los parámetros de los formularios en Porfin.'
            )
        if self.__session_id is None:
            raise AutomationError(
                'No se ha obtenido el id de la sesión en Porfin.'
            )

        url = f'{self.__base_url}/seguro_sura/FormParametersServlet'
        payload = f'parameterName={self.__forms_params}{self.__session_id}#tkn={form_id}'
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
        response = self.session.post(url, data=payload, headers=headers)
        if not response.ok:
            raise AutomationError(
                'No se pudo pasar los parámetros al formulario en Porfin.',
                detail=response.text,
            )
        return response.text

    def check_connection_user_password(self, h1_response: str) -> None:
        """Check if the password of the connection user is about to expire.

        Parameters
        ----------
        h1_response : str
            Response of the service response that returns the first HC value.

        """
        pattern = r'SU CLAVE VENCE EL (?P<expiration_date>\d{4}\/\d{1,2}\/\d{1,2}) DE NO CAMBIARLA SERA INACTIVADO'
        match = re.search(
            pattern, h1_response.upper(), re.IGNORECASE | re.DOTALL
        )
        if not match:
            return None
        expiration_date = match.groupdict().get('expiration_date', '')
        if expiration_date:
            logger.warning(
                f'La contraseña del usuario de conexión a Porfin expirará el "{expiration_date}".'
            )

    def get_framework_servlet_1(self, form_id: str) -> str:
        """Get the first HC value of the initialized form.

        Parameters
        ----------
        form_id : str
            Id of the initialized form.

        Returns
        -------
        str
            First HC value of the form.

        Raises
        ------
        AutomationError
            If HC value could not be found.

        """
        try:
            referer_url = f'{self.__base_url}/seguro_sura/LibreriasJFB/com/alfagl/seguro/formas/AGENT000.jsf?IDXFP={form_id}'
            self.session.get(referer_url)
            url = self.__form_url
            payload = f'<?xml version="1.0" encoding="ISO-8859-1"?><C1 N1="com.alfagl.seguro.formas.AGENT000" UN="" EN="" CP="F" TS="NS" BL="es-CO" TZ="America/Bogota" BOS="Windows"><M1 N1="init" /><pars ><par n1="IDXFP" v1="{form_id}" /></pars></C1>'
            headers = {
                'Content-Type': 'text/plain;charset=UTF-8',
                'Referer': referer_url,
            }
            response = self.session.post(url, data=payload, headers=headers)
            if not response.ok:
                raise AutomationError(
                    'No se pudo obtener el HC1 del formulario en Porfin.',
                    detail=response.text,
                )

            # This service might return a text within the response indicating
            # that the password of the connection user is about expire. If so,
            # the "check_connection_user_password" will send an alert mail to
            # the admins to let them know about this warning
            self.check_connection_user_password(response.text)

            json_response = xmltodict.parse(response.text)
            return json_response['C1']['@HC']
        except KeyError as e:
            detail = (
                f'CONTENT: {response.text}; FORM_ID: {form_id}; ERROR: {e}'
            )
            raise AutomationError(
                'No se encontró el HC1 del formulario en Porfin.',
                detail=detail,
            )

    def get_framework_servlet_2(self, hc1: str) -> str:
        """Get the second HC value of the initialized form.

        Parameters
        ----------
        hc1 : str
            First HC value of the form.

        Returns
        -------
        str
            Second HC value of the form.

        Raises
        ------
        AutomationError
            If HC value could not be found.

        """
        try:
            referer_url = f'{self.__base_url}/seguro_sura/1Seguro2013/com/alfagl/seguro/formas/SGENT000.jsf?cn=com.alfagl.seguro.formas.AGENT000&qm=F&pl=T&pf=com.alfagl.seguro.formas.AGENT000&ssty=SS&hcp={hc1}'
            self.session.get(referer_url)
            url = self.__form_url
            payload = f'<C1 N1="com.alfagl.seguro.formas.SGENT000" CP="F" HCPF="{hc1}" TS="SS" QM="F" PF="com.alfagl.seguro.formas.AGENT000" PL="T" ><pars ><par n1="cn" v1="com.alfagl.seguro.formas.AGENT000" /><par n1="qm" v1="F" /><par n1="pl" v1="T" /><par n1="pf" v1="com.alfagl.seguro.formas.AGENT000" /><par n1="ssty" v1="SS" /><par n1="hcp" v1="{hc1}" /></pars><M1 N1="init" /></C1>'
            headers = {
                'Content-Type': 'text/plain;charset=UTF-8',
                'Referer': referer_url,
            }
            response = self.session.post(url, data=payload, headers=headers)
            if not response.ok:
                raise AutomationError(
                    'No se pudo obtener el HC2 del formulario en Porfin.',
                    detail=response.text,
                )

            json_response = xmltodict.parse(response.text)
            return json_response['C1']['@HC']
        except KeyError as e:
            detail = f'CONTENT: {response.text}; HC1: {hc1}; ERROR: {e}'
            raise AutomationError(
                'No se encontró el HC2 del formulario en Porfin.',
                detail=detail,
            )

    def get_framework_servlet_3(self, hc2: str) -> str:
        """Get the third HC value of the initialized form.

        Parameters
        ----------
        hc2 : str
            Second HC value of the form.

        Returns
        -------
        str
            Third HC value of the form.

        Raises
        ------
        AutomationError
            If HC value could not be found.

        """
        try:
            referer_url = f'{self.__base_url}/seguro_sura/1Seguro2013/com/alfagl/seguro/formas/AGMRU032.jsf?cn=com.alfagl.seguro.formas.SGENT000&qm=F&pl=F&pf=com.alfagl.seguro.formas.SGENT000&ssty=SS&hcp={hc2}'
            self.session.get(referer_url)
            url = self.__form_url
            payload = f'<C1 N1="com.alfagl.seguro.formas.AGMRU032" CP="F" HCPF="{hc2}" TS="SS" QM="F" PF="com.alfagl.seguro.formas.SGENT000" PL="F" ><pars ><par n1="cn" v1="com.alfagl.seguro.formas.SGENT000" /><par n1="qm" v1="F" /><par n1="pl" v1="F" /><par n1="pf" v1="com.alfagl.seguro.formas.SGENT000" /><par n1="ssty" v1="SS" /><par n1="hcp" v1="{hc2}" /></pars><M1 N1="init" /></C1>'
            headers = {
                'Content-Type': 'text/plain;charset=UTF-8',
                'Referer': referer_url,
            }
            response = self.session.post(url, data=payload, headers=headers)
            if not response.ok:
                raise AutomationError(
                    'No se pudo obtener el HC3 del formulario en Porfin.',
                    detail=response.text,
                )

            json_response = xmltodict.parse(response.text)
            return json_response['C1']['@HC']
        except KeyError as e:
            detail = f'CONTENT: {response.text}; HC2: {hc2}; ERROR: {e}'
            raise AutomationError(
                'No se encontró el HC3 del formulario en Porfin.',
                detail=detail,
            )

    def get_new_form(self) -> PorfinForm:
        """Get a new form.

        Returns
        -------
        PorfinForm
            Porfin form.

        Raises
        ------
        AutomationError
            If no session found.

        """
        if not self.authenticated:
            raise AutomationError('No se ha iniciado una sesión en Porfin.')

        opened_form_id = self.open_form()
        form_id = self.form_parameters(form_id=opened_form_id)

        # The following three requests get three HC values.
        # The meaning of these HC values is unknown, but they are required
        # to perform operations such as fetch and update a user
        hc1 = self.get_framework_servlet_1(form_id)
        hc2 = self.get_framework_servlet_2(hc1)
        hc3 = self.get_framework_servlet_3(hc2)

        self.last_form = PorfinForm(form_id, hc1, hc2, hc3)
        return self.last_form

    def close_form(self, form: PorfinForm) -> None:
        """Close an opened form.

        Parameters
        ----------
        form : PorfinForm
            Porfin form.

        """
        self.last_form = None
        hc2 = form.hc2
        hc3 = form.hc3

        url = f'{self.__base_url}/seguro_sura/FrameworkServlet'
        payload = f'<C1 N1="com.alfagl.seguro.formas.AGMRU032" HC="{hc3}" CP="F" MOC="anterior" ><XD SI="" ><D N="B2" /><D N="Toolbar" /><D N="Footer" /></XD><XV E4="24" CUE="PfmenuFin" MOC="anterior" MNN="fin" CP="F" /><M1 N1="processEvent" /></C1>'
        headers = {
            'Content-Type': 'text/plain;charset=UTF-8',
            'HC': hc3,
            'Referer': f'{self.__base_url}/seguro_sura/1Seguro2013/com/alfagl/seguro/formas/AGMRU032.jsf?cn=com.alfagl.seguro.formas.SGENT000&qm=F&pl=F&pf=com.alfagl.seguro.formas.SGENT000&ssty=SS&hcp={hc2}',
        }
        self.session.post(url, headers=headers, data=payload)

    def create_session(self) -> None:
        """Create a TLSSession instance."""
        self.session = TLSSession()

    def close_session(self) -> None:
        """Close the session."""
        if hasattr(self, 'session'):
            self.session.close()

    def logout(self):
        """Logout in 3 steps and close session."""
        headers = {'Referer': self.__index_url}
        # Logout step 1
        url = f'{self.__base_url}/AlfaGL/AlfaGlLogout'
        self.session.post(url, headers=headers)
        # Logout step 2
        url = self.__index_url
        self.session.get(url, headers=headers)
        # Logout step 3
        url = self.__menu_url
        self.session.post(url, headers=headers)
        self.__session_id = None
        self.clear_cookies()
        self.close_session()
