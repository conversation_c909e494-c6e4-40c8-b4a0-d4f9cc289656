from typing import Literal, cast

from reportlab.pdfgen.canvas import <PERSON><PERSON>
from reportlab.platypus.doctemplate import SimpleD<PERSON><PERSON><PERSON>plate
from reportlab.platypus import Flowable, Paragraph

from lib.pdf.page_canvas import PageCanvas


class DocumentTemplate(SimpleDocTemplate):
    """"Creates the document template which
    will be used to build the PDF.

    Derived from the `SimpleDocTemplate` class
    in the reportlab source code.
    """

    main_logo_path: str | None = None
    """Path of the main logo."""

    secondary_logo_path: str | None = None
    """Path of the secondary logo."""

    def _startBuild(self, filename=None, canvasmaker=Canvas) -> None:
        self._calc()
        self.canv = cast(PageCanvas, self._makeCanvas(filename=filename, canvasmaker=canvasmaker))
        self.canv.main_logo_path = self.main_logo_path
        self.canv.secondary_logo_path = self.secondary_logo_path
        self.handle_documentBegin()

    def __add_entry_to_toc(self, heading_level: Literal[1, 2, 3], text: str) -> None:
        """Adds an entry to table of contents.

        Parameters
        ----------
        heading_level : Literal[1, 2, 3]
            Level.
        text : str
            Text.
        """

        key = 'h%d-%s' % (heading_level, self.seq.nextf(f'heading{heading_level}'))
        self.canv.bookmarkPage(key)

        entry_level = heading_level - 1

        # Add entry to table of contents
        self.notify(
            kind='TOCEntry',
            stuff=(entry_level, text, self.page, key))

        # Add entry to the navigation menu of the PDF
        # when opened in a PDF viewer
        self.canv.addOutlineEntry(text, key, entry_level, 0)

    def afterFlowable(self, flowable: Flowable) -> None:
        """Defines actions that shall be
        performed after a flowable is added.

        Function is called by the 'multiBuild' method of the
        'SimpleDocTemplate' class.

        Parameters
        ----------
        flowable : Flowable
            Flowable element.
        """

        if isinstance(flowable, Paragraph):
            text = flowable.getPlainText()

            # Only do this if the flowable is of type 'Heading'
            if flowable.style.name == 'Heading1':
                self.__add_entry_to_toc(1, text)
            if flowable.style.name == 'Heading2':
                self.__add_entry_to_toc(2, text)
            if flowable.style.name == 'Heading3':
                self.__add_entry_to_toc(3, text)

            # Only do this if the flowable corresponds to the
            # string above the table of contents
            if flowable.style.name == 'TableOfContents':
                key = 'h1-%s' % self.seq.nextf('heading1')
                self.canv.bookmarkPage(key)

                # Add entry to the navigation menu of the PDF
                # when opened in a PDF viewer
                self.canv.addOutlineEntry(text, key, 0, 0)
