from enum import Enum

from typing import Callable
from functools import wraps

from lib.responses import ErrorResponse
from lib.validator.error_validator import ErrorValidator


class AuthRole(Enum):
    CONSULT = 'consult'
    REMOVE = 'remove'


def auth(role: AuthRole | None = None) -> Callable:
    """Call the decorated function if client is authenticated.

    If user is not authenticated, return a 401 error.
    If user does not have the passed role, return a 403 error.

    Parameters
    ----------
    role : AuthRole | None, optional
        Role, by default None.

    Returns
    -------
    Callable
        Decorated function.

    """

    def decorator(method: Callable):
        @wraps(method)
        async def wrapper(self, *args, **kwargs):
            error_validator = ErrorValidator()
            error_message = None
            if hasattr(self.request.user, 'error_message'):
                error_message = self.request.user.error_message
            if not self.request.user.is_authenticated:
                error_validator.add(
                    'common',
                    error_message or 'Usuario no autenticado'
                )
                return ErrorResponse(error_validator, status_code=401)
            if role and not self.request.user.has_role(role.value):
                error_validator.add(
                    'common',
                    'No tiene los permisos suficientes para realizar esta acción'
                )
                return ErrorResponse(error_validator, status_code=403)
            return await method(self, *args, **kwargs)

        return wrapper

    return decorator
