from datetime import datetime, timedelta, timezone

from asyncify import asyncify
from beanie import PydanticObjectId

from dtos.consulted_user_dto import ConsultedUserResponseDto
from lib import config
from lib.active_directory import ADUser
from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.export import ExportData, File
from lib.filter import Filter, FormatFilter
from lib.pagination import Pagination
from lib.results_exporter import ResultsExporter
from models.consulted_user import ConsultedUser


class ConsultedUserService(BaseInjectable, BaseService):
    """Service to get the current stored data
    of the consulted users.
    """

    def __init__(
        self,
        exporter: ResultsExporter,
        export_data: ExportData,
        filter_: Filter,
    ) -> None:
        super().__init__()
        self.__exporter = exporter
        self.__export_data = export_data
        self.__filter = filter_

    async def create_or_update(
        self,
        consulted_user: ConsultedUser | None,
        username: str,
        ad_user: ADUser | None,
        results: list[dict[str, object]],
        error_count: int,
        warning_count: int,
        total_time: float,
    ) -> ConsultedUser:
        """Create or update a consulted user.

        Parameters
        ----------
        consulted_user : ConsultedUser | None
            Consulted user if exists.
        username : str
            Username of the consulted user.
        ad_user : ADUser | None
            Active Directory user data if found.
        results : list[dict[str, object]]
            Results of the automation.
        error_count : int
            Error count of the automation.
        warning_count : int
            Warning count of the automation.
        total_time : float
            Total time of the automation.

        Returns
        -------
        ConsultedUser
            Created/updated consulted user.

        """
        consulted_user_exp = datetime.now() + timedelta(
            minutes=config.CONSULTED_USERS_TIMEOUT_MINUTES
        )
        consulted_user_exp_utc = datetime.now(timezone.utc) + timedelta(
            minutes=config.CONSULTED_USERS_TIMEOUT_MINUTES
        )
        if consulted_user:
            await consulted_user.set(
                {
                    str(ConsultedUser.exp): consulted_user_exp,
                    str(ConsultedUser.expUTC): consulted_user_exp_utc,
                    str(ConsultedUser.data): results,
                    str(ConsultedUser.errorCount): error_count,
                    str(ConsultedUser.warningCount): warning_count,
                    str(ConsultedUser.time): total_time,
                }
            )
        else:
            consulted_user = ConsultedUser(
                username=username,
                exp=consulted_user_exp,
                expUTC=consulted_user_exp_utc,
                ad_user=ad_user,
                data=results,
                errorCount=error_count,
                warningCount=warning_count,
                time=total_time,
            )
            await consulted_user.create()
        return consulted_user

    async def list(
        self,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[ConsultedUserResponseDto]:
        """Get the list of consulted users.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter
            the consulted users, by default None.

        Returns
        -------
        Pagination[ConsultedUserResponseDto]
            Paginated list of consulted users.

        """
        query = ConsultedUser.find(ConsultedUser.isDeleted == False)  # noqa: E712

        if filter_data is not None:
            query = self.__filter.create(filter_data, query)

        pagination = Pagination[ConsultedUserResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = ConsultedUserResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def retrieve(self, id: str) -> ConsultedUserResponseDto | None:
        """Find a consulted user by id.

        Parameters
        ----------
        id : str
            Consulted user id.

        Returns
        -------
        ConsultedUserResponseDto | None
            Consulted user if found.

        """
        consulted_user = await ConsultedUser.find_one(
            ConsultedUser.id == PydanticObjectId(id)
        )
        if not consulted_user:
            return None
        return ConsultedUserResponseDto.from_orm(consulted_user)

    async def retrieve_by_username(
        self, username: str
    ) -> ConsultedUserResponseDto | None:
        """Find a consulted user by username.

        Parameters
        ----------
        username : str
            Consulted user username.

        Returns
        -------
        ConsultedUserResponseDto | None
            Consulted user if found.

        """
        consulted_user = await ConsultedUser.find_one(
            ConsultedUser.username == username.strip().upper()
        )
        if not consulted_user:
            return None
        return ConsultedUserResponseDto.from_orm(consulted_user)

    @asyncify
    def generate_pdf_file(self, consulted_user: ConsultedUser) -> File | None:
        """Generate a PDF file from the consulted user data.

        Parameters
        ----------
        consulted_user : ConsultedUser
            Consulted user.

        Returns
        -------
        File | None
            Generated PDF file.

        """
        pdf_content = self.__exporter.to_pdf(
            consulted_user,
            action='consult',
            has_privileges=False,
            include_not_found=True,
            include_errors=True,
        )
        if pdf_content is None:
            return None
        date = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
        filename = f'reporte_{consulted_user.username}_{date}'
        return self.__export_data.stream_to_file(
            stream=pdf_content, filename=filename, extension='pdf'
        )

    async def export(self, id: str) -> File | None:
        """Find a consulted user by its id and export it.

        Parameters
        ----------
        id : str
            Id of the consulted user.

        Returns
        -------
        File | None
            Exported consulted user to PDF if found.

        """
        consulted_user = await ConsultedUser.find_one(
            ConsultedUser.id == PydanticObjectId(id)
        )
        if not consulted_user:
            return None
        return await self.generate_pdf_file(consulted_user)

    async def export_by_username(self, username: str) -> File | None:
        """Find a consulted user by its username and export it.

        Parameters
        ----------
        username : str
            Username of the consulted user.

        Returns
        -------
        File | None
            Exported consulted user to PDF if found.

        """
        consulted_user = await ConsultedUser.find_one(
            ConsultedUser.username == username.strip().upper()
        )
        if not consulted_user:
            return None
        return await self.generate_pdf_file(consulted_user)
