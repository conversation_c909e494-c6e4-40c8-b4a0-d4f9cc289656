# Build stage
FROM node:lts-alpine as builder

ARG SERVER_NAME
ARG BOT_PORT
ENV SERVER_NAME=${SERVER_NAME}
ENV BOT_PORT=${BOT_PORT}

WORKDIR /app

COPY package*.json ./

RUN npm config set strict-ssl false && npm install

COPY . .

RUN npm run build

# Production stage
FROM nginx:stable-alpine

RUN apk add --no-cache --no-check-certificate \
    bash \
    curl \
    procps \
    tzdata \
    vim \
    && cp /usr/share/zoneinfo/America/Bogota /etc/localtime \
    && echo "America/Bogota" > /etc/timezone

COPY --from=builder /app/dist/spa /usr/share/nginx/html

COPY ./default.conf /etc/nginx/templates/default.conf.template
