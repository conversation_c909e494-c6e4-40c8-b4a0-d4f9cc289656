<template>
  <div class="fullscreen bg-blue text-white text-center q-pa-md flex flex-center">
    <div>
      <div style="font-size: 30vh">
        403
      </div>

      <div class="text-h2" style="opacity:.4">{{t('forbidden')}}</div>

      <q-btn
        class="q-mt-xl"
        color="white"
        text-color="blue"
        unelevated
        :label="t('return')"
        no-caps
        @click="logout"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useI18n } from 'vue-i18n';

import UserSession from 'src/lib/userSession';
//#endregion

const { t } = useI18n();

async function logout(): Promise<void> {
  await UserSession.logout();
}
</script>
