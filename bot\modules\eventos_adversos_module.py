from typing import cast
from urllib.parse import urljoin

from bs4 import ResultSet, Tag
from requests import Response

from dtos.automation_dto import (
    EventosAdversosRemovedUserResponseDto,
    EventosAdversosResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.tools import get_form_data, get_html_soup, retry, strip_text

APP_URL = 'https://www.serviciosensaludsura.com/administrator/?s7ra1dmin1ps5surameric4na'
BASE_PLATFORM_URL = 'https://www.serviciosensaludsura.com/administrator/%s'
USERS_FORM_PATH = 'index.php?option=com_users'
MIN_COLUMNS_COUNT = 6


class EventosAdversosModule(BaseModule):
    """Provide functions to consult and remove
    a user on Eventos Adversos.
    """

    def _get_login_form(self, use_retiros_user: bool = False) -> FormData:
        """Get the login form.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        FormData
            Login form.

        Raises
        ------
        AutomationError
            If login form could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de login.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de login.',
        )
        form_data.action = urljoin(response.url, form_data.action)
        form_data.data['lang'] = 'es-ES'
        form_data.data['username'] = (
            str(config.EVENTOS_ADVERSOS_RETIROS_USER)
            if use_retiros_user
            else str(config.EVENTOS_ADVERSOS_USER)
        )
        form_data.data['passwd'] = (
            str(config.EVENTOS_ADVERSOS_RETIROS_PASSWORD)
            if use_retiros_user
            else str(config.EVENTOS_ADVERSOS_PASSWORD)
        )
        return form_data

    def _send_login_form(self, login_form: FormData) -> str:
        """Send the login form.

        Parameters
        ----------
        login_form : FormData
            Login form.

        Returns
        -------
        str
            Redirection response URL.

        Raises
        ------
        AutomationError
            If login form could not be sent.

        """
        headers = {'Referer': APP_URL}
        response = self.session.request(
            method=login_form.method,
            url=login_form.action,
            data=login_form.data,
            headers=headers,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de login.',
                detail=response.text,
            )
        return response.url

    def _get_users_form_response(self, redirection_url: str) -> Response:
        """Get the HTML users form and return the response instance.

        Parameters
        ----------
        redirection_url : str
            Redirection URL.

        Returns
        -------
        Response
            HTML users form response.

        Raises
        ------
        AutomationError
            If users form could not be fetched.

        """
        headers = {'Referer': redirection_url}
        response = self.session.get(
            BASE_PLATFORM_URL % USERS_FORM_PATH, headers=headers
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de usuarios.',
                detail=response.text,
            )
        return response

    def _extract_user_data_cells(self, user_data_response: str) -> ResultSet:
        """Extract the user data cells.

        Parameters
        ----------
        user_data_response : str
            HTML user data.

        Returns
        -------
        ResultSet
            User data cells.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_data_response)
        user_data_table = soup.find(id='userList')
        if not user_data_table:
            raise NotFoundError()
        user_data_table_body = cast(Tag, user_data_table).tbody
        if not user_data_table_body:
            raise NotFoundError()
        user_data_row = user_data_table_body.find('tr')
        if not user_data_row:
            raise NotFoundError()
        user_data_cells = cast(Tag, user_data_row).find_all('td')
        if not user_data_cells or not len(user_data_cells) > MIN_COLUMNS_COUNT:
            raise NotFoundError()
        return user_data_cells

    def _extract_groups(
        self,
        user_data_cells: ResultSet,
    ) -> list[str]:
        """Extract the groups of the user.

        Parameters
        ----------
        user_data_cells : ResultSet
            User data cells.

        Returns
        -------
        list[str]
            User groups.

        """
        groups_text = user_data_cells[5].span.get('title')
        if groups_text:
            splitted_groups_text = groups_text.split('<br />')[1:]
            return [group.strip() for group in splitted_groups_text]
        return []

    def _extract_user_state(self, user_data_cells: ResultSet) -> bool:
        """Extract the state of the user.

        Parameters
        ----------
        user_data_cells : ResultSet
            User data cells.

        Returns
        -------
        bool
            Whether user is enabled.

        """
        enabled_class = user_data_cells[3].span.get('class', ['none'])[0]
        true_class_name = 'icon-publish'
        return enabled_class == true_class_name

    def _create_user_data_dto(
        self,
        user_data_response: str,
    ) -> EventosAdversosResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_data_response : str
            HTML user info.

        Returns
        -------
        EventosAdversosResponseDto
            Requested user data.

        """
        user_data_cells = self._extract_user_data_cells(user_data_response)
        enabled = self._extract_user_state(user_data_cells)
        groups = self._extract_groups(user_data_cells)
        return EventosAdversosResponseDto(enabled=enabled, groups=groups)

    def _send_consult_user_form(
        self, username: str, response: Response
    ) -> Response:
        """Consult the user by its username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        response : Response
            Response containing the users form.

        Returns
        -------
        Response
            User HTML info response.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de consulta del usuario.',
        )
        form_data.action = urljoin(response.url, form_data.action)
        form_data.data['filter[search]'] = username
        headers = {'Referer': response.url}
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
            headers=headers,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response

    def disable_user(
        self, response: Response
    ) -> EventosAdversosRemovedUserResponseDto:
        """Disables the user.

        Parameters
        ----------
        response : Response
            Response of the `consult_user` function.

        Returns
        -------
        EventosAdversosRemovedUserResponseDto
            Disabled user response.

        Raises
        ------
        AutomationError
            User could not be disabled.
        """

        user_data_cells = self._extract_user_data_cells(response.text)
        user_id = strip_text(user_data_cells[-1].text)
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de retiro del usuario.',
        )
        form_data.action = urljoin(response.url, form_data.action)
        form_data.data.update(
            {
                'cid[]': user_id,
                'checkall-toggle': None,
                'batch[group_action]': 'add',
                'batch[reset_id]': None,
                'task': 'users.block',
                'boxchecked': 1,
            }
        )
        headers = {'Referer': response.url}
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
            headers=headers,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=response.text,
            )
        return EventosAdversosRemovedUserResponseDto(
            enabled=False, message='Usuario retirado correctamente.'
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> EventosAdversosResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        EventosAdversosResponseDto
            Requested user data.

        """
        try:
            self.create_session()
            login_form = self._get_login_form()
            redirection_url = self._send_login_form(login_form)
            users_form_response = self._get_users_form_response(
                redirection_url
            )
            user_info_response = self._send_consult_user_form(
                username, users_form_response
            )
            return self._create_user_data_dto(user_info_response.text)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(
        self,
        username: str,
    ) -> EventosAdversosRemovedUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        EventosAdversosRemovedUserResponseDto
            Disabled user data.

        """
        try:
            self.create_session()
            login_form = self._get_login_form(use_retiros_user=True)
            redirection_url = self._send_login_form(login_form)
            users_form_response = self._get_users_form_response(
                redirection_url
            )
            user_info_response = self._send_consult_user_form(
                username, users_form_response
            )
            return self.disable_user(user_info_response)
        finally:
            self.close_session()
