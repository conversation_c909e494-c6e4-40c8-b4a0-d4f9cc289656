from i18n import I18<PERSON>

from lib.validator.error_validator import <PERSON>rrorValidator
from lib.validator.validator import Validator


DataValidatorError = dict[str, list[str]]


class DataValidatorContainer:
    field: str
    validator: Validator

    def __init__(self, field: str, validator: Validator) -> None:
        self.field = field
        self.validator = validator


class DataValidator:
    def __init__(self):
        self.is_valid: bool = True
        self.validators: list[DataValidatorContainer] = []
        #self.errors: DataValidatorError = {}
        self.error: ErrorValidator = ErrorValidator()

    def add(self, field: str, validator: Validator) -> None:
        self.validators.append(DataValidatorContainer(field, validator))

    async def validate(self) -> bool:
        self.is_valid = True
        self.error.clear()

        for validator in self.validators:
            if not await validator.validator.validate():
                self.error.add(validator.field, validator.validator.errors)
                #self.errors[validator.field] = validator.validator.errors
                self.is_valid = False

        return self.is_valid

    def get_error(self, i18n: I18N) -> ErrorValidator:
        for field, field_errors in self.error.errors.items():
            messages = []
            for field_error in field_errors:
                if isinstance(field_error, str):
                    messages.append(i18n(field_error))
                else:
                    messages.extend([i18n(message, format_values) for message, format_values in field_error.items()])
            self.error.errors[field] = messages
        return self.error
