from typing import Any

from dtos.automation_dto import CAServiceDeskResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import get_nested, retry, xml_to_dict

APP_URL = (
    'https://ca-sd-ppal.suramericana.com:8443/axis/services/USD_R11_WebService'
)
IS_ACTIVE_FLAG = '0'


class CAServiceDeskModule(BaseModule):
    """Provide a function to consult the state and roles
    of a user on CA Service Desk.
    """

    def _get_response_body(
        self,
        content: dict[str, Any],
        response_key: str,
        return_key: str,
    ) -> str:
        body = content['soapenv:Envelope']['soapenv:Body']
        return body[response_key][return_key]

    def _login(self) -> str:
        """Perform the authentication.

        Returns
        -------
        str
            Session token.

        Raises
        ------
        AutomationError
            If error when sending the login request.

        """
        payload = """
        <soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ser="http://www.ca.com/UnicenterServicePlus/ServiceDesk">
            <soapenv:Header/>
            <soapenv:Body>
                <ser:login>
                    <username>{username}</username>
                    <password>{password}</password>
                </ser:login>
            </soapenv:Body>
        </soapenv:Envelope>
        """.format_map(
            {
                'username': str(config.APPS_CONNECTION_USER),
                'password': str(config.APPS_CONNECTION_PASSWORD),
            }
        )
        headers = {'SOAPAction': 'login', 'Content-Type': 'application/xml'}
        response = self.session.post(APP_URL, headers=headers, data=payload)
        if not response.ok:
            raise AutomationError(
                'Ha ocurrido un error en la petición del login.',
                detail=response.text,
            )
        return self._extract_session_token(response.text)

    def _extract_session_token(self, response_text: str) -> str:
        """Extract the session token from login response.

        Parameters
        ----------
        response_text : str
            Login response content.

        Returns
        -------
        str
            Session token.

        Raises
        ------
        AutomationError
            If session token could not be obtained.

        """
        try:
            return self._get_response_body(
                xml_to_dict(response_text),
                'loginResponse',
                'loginReturn',
            )
        except Exception as e:
            raise AutomationError(
                'No se pudo obtener el token de sesión.',
                detail=f'CONTENT: {response_text}; ERROR: {str(e)}',
            ) from e

    def _extract_results(self, response_text: str) -> Any:
        """Parse the XML response content into a dict and
        extract the results.

        Parameters
        ----------
        response_text : str
            XML response content.

        Returns
        -------
        Any
            Results.

        """
        content = xml_to_dict(response_text)
        xml_data = self._get_response_body(
            content,
            'doSelectResponse',
            'doSelectReturn',
        )
        parsed_data = xml_to_dict(xml_data)
        return get_nested(parsed_data, 'UDSObjectList').get('UDSObject')

    def _send_consult_user_form(
        self, session_token: str, username: str
    ) -> Any:
        """Consult a user.

        Parameters
        ----------
        session_token : str
            Session token.
        username : str
            Username.

        Returns
        -------
        Any
            User data.

        Raises
        ------
        AutomationError
            If user data could not be fetched.
        NotFoundError
            User was not found.

        """
        payload = """
        <soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ser="http://www.ca.com/UnicenterServicePlus/ServiceDesk">
            <soapenv:Header/>
            <soapenv:Body>
                <ser:doSelect>
                    <sid>{session_token}</sid>
                    <objectType>cnt</objectType>
                    <whereClause>userid = '{username}'</whereClause>
                    <maxRows>10</maxRows>
                    <attributes>
                        <string>delete_flag</string>
                    </attributes>
                </ser:doSelect>
            </soapenv:Body>
        </soapenv:Envelope>
        """.format_map({'session_token': session_token, 'username': username})
        headers = {'SOAPAction': 'doSelect', 'Content-Type': 'application/xml'}
        response = self.session.post(APP_URL, headers=headers, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        user_data = self._extract_results(response.text)
        if not user_data:
            raise NotFoundError()
        return user_data

    def _extract_user_state(self, user_data: dict[str, Any]) -> bool:
        """Extract the user state.

        Parameters
        ----------
        user_data : dict[str, Any]
            User data.

        Returns
        -------
        bool
            Whether user is active.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user state could not be obtained.

        """
        try:
            deleted_flag = user_data['Attributes']['Attribute']['AttrValue']
            return deleted_flag == IS_ACTIVE_FLAG
        except KeyError:
            return False
        except Exception as e:
            raise AutomationError(
                'No se pudo obtener el estado del usuario.',
                detail=f'CONTENT: {user_data!s}; ERROR: {str(e)}',
            ) from e

    def _get_user_roles(self, session_token: str, username: str) -> list[str]:
        """Find a user by its username and return its roles.

        Parameters
        ----------
        session_token : str
            Session token.
        username : str
            Username.

        Returns
        -------
        list[str]
            User roles.

        Raises
        ------
        AutomationError
            If user roles could not be fetched.

        """
        payload = """
        <soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ser="http://www.ca.com/UnicenterServicePlus/ServiceDesk">
            <soapenv:Header/>
            <soapenv:Body>
                <ser:doSelect>
                    <sid>{session_token}</sid>
                    <objectType>cnt_role</objectType>
                    <whereClause>contact.userid = '{username}'</whereClause>
                    <maxRows>1000</maxRows>
                    <attributes>
                        <string>role_obj.name</string>
                    </attributes>
                </ser:doSelect>
            </soapenv:Body>
        </soapenv:Envelope>
        """.format_map({'session_token': session_token, 'username': username})
        headers = {'SOAPAction': 'doSelect', 'Content-Type': 'application/xml'}
        response = self.session.post(APP_URL, headers=headers, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar los roles del usuario.',
                detail=response.text,
            )
        return self._extract_user_roles(response.text)

    def _extract_user_roles(self, response_text: str) -> list[str]:
        """Extract the roles of the user.

        Parameters
        ----------
        response_text : str
            User data response content.

        Returns
        -------
        list[str]
            Roles of the user.

        Raises
        ------
        AutomationError
            If roles could not be obtained.

        """
        try:
            roles = self._extract_results(response_text)
            if not roles:
                return []
            if isinstance(roles, list):
                return [
                    role['Attributes']['Attribute']['AttrValue']
                    for role in roles
                ]
            return [roles['Attributes']['Attribute']['AttrValue']]
        except KeyError:
            return []
        except Exception as e:
            raise AutomationError(
                'No se pudo obtener los roles del usuario.',
                detail=f'CONTENT: {response_text}; ERROR: {str(e)}',
            ) from e

    def _logout(self, session_token: str) -> None:
        """Logout.

        Parameters
        ----------
        session_token : str
            Session token.

        """
        payload = """
        <soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ser="http://www.ca.com/UnicenterServicePlus/ServiceDesk">
            <soapenv:Header/>
            <soapenv:Body>
                <ser:logout>
                    <sid>{session_token}</sid>
                </ser:logout>
            </soapenv:Body>
        </soapenv:Envelope>
        """.format_map({'session_token': session_token})
        headers = {'SOAPAction': 'logout', 'Content-Type': 'application/xml'}
        self.session.post(APP_URL, headers=headers, data=payload)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> CAServiceDeskResponseDto:
        """Consult the state and roles of a user.

        Parameters
        ----------
        username : str
            Username to consult.

        Returns
        -------
        CAServiceDeskResponseDto
            User data.

        """
        try:
            self.create_session()
            session_token = self._login()
            user_data = self._send_consult_user_form(session_token, username)
            active = self._extract_user_state(user_data)
            roles = self._get_user_roles(session_token, username)
            self._logout(session_token)
            return CAServiceDeskResponseDto(active=active, roles=roles)
        finally:
            self.close_session()
