<template>
  <div v-if="!props.application.ignored">
    <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('startTime') }}:</span> {{ dateParse(new Date(props.application.startTime * 1000)) }}
    <br>
    <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('endTime') }}:</span> {{ dateParse(new Date(props.application.endTime * 1000)) }}
    <br>
    <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ props.removedUser ? t('deletionTime') : t('queryTime') }}:</span> {{ secondsToSchedule(props.application.timeDiff) }}
  </div>
  <q-separator v-if="!props.application.ignored" spaced />
  <template v-if="props.application.data !== null && props.application.data !== undefined">
    <div v-if="props.removedUser">
      <div v-if="props.application.data.hasOwnProperty('message')">
        <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('message') }}:</span>
        {{ props.application.data.message }}
      </div>
    </div>
    <div v-for="(value, key) in props.application.data" :key="key">
      <div v-if="Array.isArray(value)">
        <div :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t(key) }}: {{ (value as Array<any>).length }}</div>
        <ul class="value-list">
          <li v-for="(subValue, subIndex) in value" :key="subIndex">
            <span v-html="formatValue(subValue)"></span>
          </li>
        </ul>
      </div>
      <div v-else-if="(String(key) !== 'message' && String(key) !== 'warning') || !props.removedUser">
        <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t(key) }}:</span> {{ formatValue(value) }}
      </div>
    </div>
  </template>
  <div v-else>
    <span v-if="props.application.userNotFound" :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ props.application.error }}</span>
    <div v-else-if="props.application.ignored">
      <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('warning') }}:</span> {{ t('applicationWasIgnored') }}
      <br>
      <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('reason') }}:</span> {{ props.application.warning ?? t('userCouldNotBeConsultedBecauseOfMissingData') }}
    </div>
    <div v-else>
      <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('error') }}:</span> {{ t('userCouldNotBeConsulted') }}
      <br>
      <span :class="$q.dark.isActive ? 'text-bold' : textStyle">{{ t('reason') }}:</span> {{ props.application.error }}
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';
import DOMPurify from 'dompurify';

import type { Application } from 'src/lib/interfaces';
import { dateParse, secondsToSchedule } from 'src/lib/tools';
//#endregion

//#region types
interface ApplicationCardProps {
  application: Application;
  removedUser?: boolean;
}
//#endregion

const props = defineProps<ApplicationCardProps>();

const { t } = useI18n();

const $q = useQuasar();

const warning = computed<boolean>(() => {
  return props.application.data?.warning === true;
});

const textStyle = computed<string>(() => {
  if(props.application.error) {
    return props.application.userNotFound ? 'text-bold text-orange-7' : 'text-bold text-negative';
  }
  if(props.application.ignored || warning.value) {
    return 'text-bold text-orange-7';
  }
  return 'text-bold text-primary';
});

const formatValue = (value: unknown): string => {
  if (value == null || value == undefined) return 'N/A';
  if (typeof value == 'string' && value.trim() == '') return 'N/A';
  if (typeof value === 'boolean') {
    return value ? 'Si' : 'No';
  }
  if (Array.isArray(value)) {
    return value.map(item => {
      if (typeof item === 'object' && item !== null) {
        return Object.entries(item)
          .map(([key, val]) => `<b>${t(key)}</b>: ${DOMPurify.sanitize(formatValue(val))}`)
          .join(', ');
      }
      return String(item);
    }).join(', ');
  } else if (typeof value === 'object' && value !== null) {
    return Object.entries(value)
      .map(([key, val]) => `<b>${t(key)}</b>: ${DOMPurify.sanitize(formatValue(val))}`)
      .join(', ');
  }
  return String(value);
}
</script>
