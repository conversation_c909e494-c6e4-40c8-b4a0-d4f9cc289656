server {
    listen 443 ssl;
    http2 on;
    server_name ${SERVER_NAME};

    ssl_certificate     /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    access_log /dev/stdout;
    error_log /dev/stderr;

    location = /proxy/health {
        return 200;
    }

    location ~ ^/automation/remove_(users|user/idm)$ {
        proxy_pass https://bot_remove:8010;
    }

    location / {
        proxy_pass https://bot_consult:8010;
    }
}
