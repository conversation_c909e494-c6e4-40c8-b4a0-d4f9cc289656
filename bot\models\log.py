from typing import Any, Literal
from beanie import PydanticObjectId

from lib.base_model import BaseModel


class Log(BaseModel):
    transactionId: PydanticObjectId
    username: str
    moduleName: str
    moduleFuncArgsSpec: str
    moduleFuncArgs: list[object]
    application: str
    applicationName: str
    data: dict[str, Any] | None = None
    message: str | None = None
    detail: str | None = None
    startTime: float = 0.0
    endTime: float = 0.0
    timeDiff: float = 0.0
    userNotFound: bool = False
    ignored: bool = False
    warning: str | None = None
    status: Literal['done', 'error', 'ignored', 'user_not_found']
    action: Literal['consult', 'remove'] = 'consult'
    consultedBy: str | None = None
    removedBy: str | None = None
