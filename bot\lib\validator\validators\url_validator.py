import re
import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class UrlValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, message: str | None = None) -> None:
        super().__init__(value)
        self.message = message if message else 'invalid_url'

    async def validate(self) -> bool:
        value = self.value()
        validate_fields: str = r'(^https\:\/\/|^http\:\/\/)'
        self.is_valid = re.search(validate_fields, value, re.I) is not None if value else True
        return self.is_valid
