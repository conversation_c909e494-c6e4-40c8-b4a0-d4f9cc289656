import re
from dataclasses import dataclass
from typing import cast
from urllib.parse import parse_qs, urlparse

from bs4 import BeautifulSoup, Tag
from requests import Response

from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.tools import get_form_data, get_html_soup, strip_text

BASE_PLATFORM_URL = 'https://s1.ariba.com/Sourcing/Main/aw'
ADMIN_LINK_CLASS = 'leg-p-b-10 a-action-lbl'
ADMIN_USERS_OPTION_CLASS = 'bold'
ADMIN_USERS_LINK_CLASS = 'leg-p-r-3 leg-lh-15 a-arc-branch-leaf noWrap'
SEARCH_USER_INPUT_CLASS = 'w-txt w-txt-dsize'
SEARCH_USER_BUTTON_CLASS = 'w-btn w-btn-primary aw7_w-btn-primary'
USER_STATE_DIV_CLASS = 'a-arw-table-buttons'
SELECTED_USER_CLASS = 'tableBody'
ACTIONS_BUTTON_CLASS = 'is-inline w-pulldown-button'
ACTIONS_TABLE_BUTTONS_CLASS = 'leg-flt-r a-arw-table-buttons'
GROUPS_TAB_CLASS = 'w-page-tabs'
GROUP_ROW_CLASS = 'tableRow1'
EDIT_USER_GROUPS_BUTTON_CLASS = (
    'tableFooter tableFooterLeft leg-txt-aln-l w-tbl-footer'
)
ADMIN_PAGE_AWPX = (
    'domainObject:Administration,activity:Manage,app:ariba.asm.'
    'appbase.ASMApplication,page:ariba/htmlui/admin/ARPAdminHomePage'
)
USERS_FORM_AWPX = (
    'domainObject:UserManager,activity:Manage,activity:Create,'
    'activity:ImportExport,misc:UserData,misc:Delegation,misc:'
    'EnterpriseUsers,misc:EnterpriseGroups,page:'
    'ariba/htmlui/admin/objectManager/ARPObjectManager,'
    'app:ariba.asm.appbase.ASMApplication'
)
CONSULTED_USER_PAGE_AWPX = (
    'app:ariba.asm.appbase.ASMApplication,page:ariba/htmlui/admin'
    '/objectManager/ARPObjectManagerObjectDetails'
)
EDIT_USER_PAGE_AWPX = (
    'app:ariba.asm.appbase.ASMApplication,page:ariba/htmlui/admin/'
    'objectManager/ARPObjectEditing'
)


@dataclass
class AribaUserState:
    """State of the user. Whether is locked and/or active."""

    locked: bool
    active: bool


@dataclass
class AribaResponse:
    """Store the response of a request in Ariba."""

    response: Response
    awr: str


def extract_aw_response_id(response_text: str) -> str:
    """Extract the value for the awr param.

    Parameters
    ----------
    response_text : str
        HTML response text.

    Returns
    -------
    str
        The awr param value.

    """
    match = re.search(r'initParams\(\'([^\']+)\',', response_text)
    if not match:
        return ''
    return str(match.group(1))


def extract_awssk_param(login_response: Response) -> str:
    """Extract the awssk param required to perform
    the next requests involved in the user consultation.

    Parameters
    ----------
    login_response : Response
        Response of the third redirection form.

    Returns
    -------
    str
        The awssk param value.

    Raises
    ------
    AutomationError
        The awssk param was not found.

    """
    parsed = urlparse(login_response.url)
    awssk = parse_qs(parsed.query).get('awssk')
    if not awssk:
        detail = f'URL: {login_response.url}; CONTENT: {login_response.text}'
        raise AutomationError(
            'No se encontró el parámetro awssk para las peticiones.',
            detail=detail,
        )
    return awssk[0]


def extract_logout_button_id(soup: BeautifulSoup) -> str:
    """Extract the logout button ID.

    Parameters
    ----------
    soup : BeautifulSoup
        Soup of the main page response content.

    Returns
    -------
    str
        Logout button ID.

    Raises
    ------
    AutomationError
        If logout button was not found.

    """
    a = cast(Tag | None, soup.find('a', {'_mid': 'Preferences'}))
    div = cast(Tag | None, soup.find(id='Preferences'))
    if not a or not a.get('id') or not div or not div.a or not div.a.get('id'):
        raise AutomationError(
            'No se encontró el botón de logout.',
            detail=str(soup),
        )
    return str(a['id']) + ',' + str(div.a['id'])


def extract_admin_tab_element_id(soup: BeautifulSoup) -> str:
    """Extract the admin tab element ID.

    Parameters
    ----------
    soup : BeautifulSoup
        Soup of the main page response content.

    Returns
    -------
    str
        Admin tab element ID.

    Raises
    ------
    AutomationError
        If admin page link was not found.

    """
    td = cast(Tag | None, soup.find('td', {'class': ADMIN_LINK_CLASS}))
    if not td or not td.a or not td.a.get('id'):
        raise AutomationError(
            'No se pudo encontrar el link de la página de administración',
            detail=str(soup),
        )
    return str(td.a['id'])


def extract_admin_users_options_element_id(admin_page_content: str) -> str:
    """Extract admin users element ID.

    Parameters
    ----------
    admin_page_content : str
        Admin page response content.

    Returns
    -------
    str
        Admin users element ID.

    Raises
    ------
    AutomationError
        If admin menu options were not found.
    AutomationError
        Id admin users dropdown was not found.

    """
    soup = get_html_soup(admin_page_content)
    tr = soup.find_all('tr', {'class': ADMIN_USERS_OPTION_CLASS})
    if len(tr) < 2:
        raise AutomationError(
            'No se encontraron las opciones del menú de administración.',
            detail=admin_page_content,
        )
    links = tr[1].find_all('a')
    if len(links) < 2 or not links[1].get('id'):
        raise AutomationError(
            'No se encontró el dropdown de administración de usuarios.',
            detail=admin_page_content,
        )
    return links[1]['id']


def extract_admin_users_link_element_id(expanded_menu_content: str) -> str:
    """Extract the admin users element ID.

    Parameters
    ----------
    expanded_menu_content : str
        Expanded menu response content.

    Returns
    -------
    str
        Admin users link element ID.

    Raises
    ------
    AutomationError
        If admin users link was not found.

    """
    soup = get_html_soup(expanded_menu_content)
    td = soup.find_all('td', {'class': ADMIN_USERS_LINK_CLASS})
    if len(td) < 3 or not td[3].a or not td[3].a.get('id'):
        raise AutomationError(
            'No se encontró el link de administración de usuarios.',
            detail=expanded_menu_content,
        )
    return td[3].a['id']


def extract_422_input_name(soup: BeautifulSoup) -> str:
    """Extract the 422 input field name.

    Parameters
    ----------
    soup : BeautifulSoup
        Soup of the users form.

    Returns
    -------
    str
        Input field name.

    Raises
    ------
    AutomationError
        If input field was not found.

    """
    input_ = cast(
        Tag | None, soup.find('input', {'class': SEARCH_USER_INPUT_CLASS})
    )
    if not input_ or not input_.get('name'):
        raise AutomationError(
            'No se encontró el campo del 422.',
            detail=str(soup),
        )
    return str(input_['name'])


def extract_search_button_id(soup: BeautifulSoup) -> str:
    """Extract the search button ID.

    Parameters
    ----------
    soup : BeautifulSoup
        Soup of the users form.

    Returns
    -------
    str
        Search button ID.

    Raises
    ------
    AutomationError
        If button was not found.

    """
    button = cast(
        Tag | None, soup.find('button', {'class': SEARCH_USER_BUTTON_CLASS})
    )
    if not button or not button.get('id'):
        raise AutomationError(
            'No se encontró el botón para buscar el usuario.',
            detail=str(soup),
        )
    return str(button['id'])


def extract_user_selection_form(soup: BeautifulSoup) -> FormData:
    """Extract the user selection form.

    Parameters
    ----------
    soup : BeautifulSoup
        Soup of the consulted user response content.

    Returns
    -------
    FormData
        User selection form.

    Raises
    ------
    AutomationError
        If user selection form was not found.
    NotFoundError
        If user was not found.

    """
    forms = soup.find_all('form')
    if not forms or not len(forms) > 1:
        raise AutomationError(
            'No se encontró el formulario de selección del usuario.',
            detail=str(soup),
        )
    form_data = get_form_data(
        forms[1],
        'No se encontró el formulario de selección del usuario.',
    )
    user_data_div = forms[1].find('div', {'class': SELECTED_USER_CLASS})
    if not user_data_div or not user_data_div.a:
        raise NotFoundError()
    form_data.data['awsn'] = user_data_div.a['id']
    return form_data


def extract_groups_tab_id(soup: str | BeautifulSoup) -> str:
    """Extract groups tab ID.

    Parameters
    ----------
    soup : str | BeautifulSoup
        User details response content or soup.

    Returns
    -------
    str
        Groups tab ID.

    Raises
    ------
    AutomationError
        Id user details tab bar was not found.
    AutomationError
        If groups tab was not found.

    """
    if isinstance(soup, str):
        soup = get_html_soup(soup)

    tabs_div = soup.find('div', {'class': GROUPS_TAB_CLASS})
    if not tabs_div:
        raise AutomationError(
            'No se encontró la barra de pestañas en la página del usuario.',
            detail=str(soup),
        )
    links = cast(Tag, tabs_div).find_all('a')
    if len(links) < 4 or not links[3].get('id'):
        raise AutomationError(
            'No se encontró la pestaña de grupos del usuario.',
            detail=str(soup),
        )
    return links[3]['id']


def extract_user_groups(raw_user_groups: str) -> list[str]:
    """Extract the groups of the user.

    Parameters
    ----------
    raw_user_groups : str
        Raw content containing the groups of the user.

    Returns
    -------
    list[str]
        Groups of the user.

    """
    soup = get_html_soup(raw_user_groups)
    data_rows = soup.find_all('tr', {'class': GROUP_ROW_CLASS})
    if not data_rows:
        return []
    try:
        return [
            strip_text(data_row.table.tr.td.a.text) for data_row in data_rows
        ]
    except (AttributeError, TypeError):
        return []
