import type { AxiosError } from 'axios';

import Validator from 'src/lib/validator/validator';
import DataValidator from 'src/lib/validator/dataValidator';
import ErrorValidator from 'src/lib/validator/errorValidator';
import { i18n } from 'src/boot/i18n';


class Form<D, E> {
  public data: D;
  public errors?: E;
  public dataValidator: DataValidator;

  public constructor(data: D, errors?: E) {
    this.data = data;
    this.errors = errors;
    this.dataValidator = new DataValidator();
  }

  public addValidator<V>(field: string, validator: Validator<V>): void {
    this.dataValidator.add<V>(field, validator);
  }

  public async validate(): Promise<boolean> {
    this.clearErrors();
    const isValid = await this.dataValidator.validate();
    if(!isValid) this.setErrors(this.dataValidator.error);
    return isValid;
  }

  public clearErrors(): void {
    if(!this.errors) return;
    Object.entries(this.errors).forEach(([, value]) => {
      (value as string[]).splice(0, (value as string[]).length);
    });
  }

  public setErrors(catchError: AxiosError | ErrorValidator): void {
    let errorsData: unknown = undefined;
    try {
      errorsData = (catchError as AxiosError).response?.data;
      if(errorsData == undefined || errorsData == null) throw new Error();
    } catch {
      if(!(catchError as ErrorValidator).errors || Object.keys((catchError as ErrorValidator).errors).length == 0) {
        if((catchError as Error).message == 'Network Error') {
          (catchError as ErrorValidator).errors = ({ common:[ i18n.global.t('networkError')] });
        } else {
          const statusText = (catchError as AxiosError).response?.statusText;
          (catchError as ErrorValidator).errors = ({ common:[ statusText ? i18n.global.t('anErrorHasOccurredWithMessage', {error: statusText}) : i18n.global.t('anErrorHasOccurred')] });
        }
      }
      errorsData = (catchError as ErrorValidator).errors;
    }
    Object.entries(errorsData as ErrorValidator).forEach(([index, value]) => {
      const indexError = index as Extract<keyof E, string>;
      if(!this.errors) return;
      const errors: unknown = this.errors[indexError];
      (errors as string[]).push(...value as string[]);
    });
  }
}

export default Form;
