<template>
  <q-dialog v-model="modelValue" persistent>
    <q-card :style="{width: props.width, maxWidth: props.maxWidth}">
      <q-toolbar>
        <q-avatar>
          <q-icon :name="icon" :color="iconColor" size="1.5em" />
        </q-avatar>
        <q-toolbar-title><span class="text-weight-bold">{{ props.title }}</span></q-toolbar-title>
      </q-toolbar>
      <q-card-section v-if="props.message">
        {{ props.message }}
      </q-card-section>
      <slot name="body"></slot>
      <q-card-actions align="right">
        <div class="q-pl-sm">
          <template v-for="(action, index) in actions" :key="index">
            <q-btn class="q-mr-sm" :color="action.color ?? 'primary'" :label="action.label" :v-close-popup="action.close" @click="action.callback" :disable="action.disabled" />
          </template>
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
//#endregion

//#region types
export type ModalType = 'success' | 'warning' | 'error' | 'info';

export interface ModalAction {
  label: string;
  callback(): void;
  color?: string;
  disabled?: boolean;
  close?: boolean;
}

interface ModalProps {
  modelValue: boolean;
  title: string;
  message?: string;
  type?: ModalType;
  icon?: string;
  iconColor?: string;
  width?: string;
  maxWidth?: string;
  actions?: ModalAction[];
  closeCallback?(): void;
  confirmModal?: boolean;
  disableContinueAction?: boolean;
  cancelActionColor?: string;
  cancelActionLabel?: string;
  cancelActionClose?: boolean;
  continueActionColor?: string;
  continueActionLabel?: string;
  continueActionClose?: boolean;
}

interface ModalEmits {
  (e: 'update:modelValue', modelValue: boolean): void;
  (e: 'onContinue'): void;
  (e: 'onCancel'): void;
}
//#endregion

const { t } = useI18n();

const props = withDefaults(defineProps<ModalProps>(), {
  icon: 'info',
  iconColor: 'info',
  confirmModal: false,
  closeCallback: () => undefined,
});

const emits = defineEmits<ModalEmits>();

const icon = computed<string>(() => {
  if(!props.type) return props.icon;
  switch (props.type) {
    case 'success':
      return 'check_circle';
    case 'warning':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'info';
  }
});

const iconColor = computed<string>(() => {
  if(!props.type) return props.iconColor;
  switch (props.type) {
    case 'success':
      return 'positive';
    case 'warning':
      return 'warning';
    case 'error':
      return 'negative';
    default:
      return 'info';
  }
});

const actions = computed<ModalAction[]>(() => {
  if(props.confirmModal) return [
    {label: props.continueActionLabel ?? t('continue'), callback: () => emits('onContinue'), color: props.continueActionColor ? props.continueActionColor : 'warning', disabled: props.disableContinueAction, close: props.continueActionClose},
    {label: props.cancelActionLabel ?? t('cancel'), callback: () => emits('onCancel'), color: props.cancelActionColor ? props.cancelActionColor : 'secondary', close: props.cancelActionClose},
  ];
  return props.actions ? props.actions : [
    {label: t('close'), callback: props.closeCallback, close: true},
  ];
});

const modelValue = computed<boolean>({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emits('update:modelValue', value);
  }
});
</script>
