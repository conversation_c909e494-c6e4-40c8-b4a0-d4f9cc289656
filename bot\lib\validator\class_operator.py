import types

from typing import Callable
from abc import ABC, abstractmethod


class ClassOperator(ABC):
  def __init__(self, eval_condition: bool | Callable[[object], bool]):
    self.__eval_condition: bool | Callable[[object], bool] = eval_condition
    self.is_valid: bool = True

  def eval_condition(self) -> bool:
    return self.__eval_condition() if isinstance(self.__eval_condition, types.FunctionType) else self.__eval_condition  # type: ignore

  @abstractmethod
  async def validate() -> bool:
    pass
