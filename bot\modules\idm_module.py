from typing import Literal, cast

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import IDMEmployeeResponseDto, IDMResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.tools import get_form_data, get_html_soup, retry

APP_URL = 'https://giga.suranet.com/iam/im/identityEnv/imcss/sura/index.jsp'
BASE_PLATFORM_URL = (
    'https://giga.suranet.com/iam/im/identityEnv/imcss/sura/index.jsp%s'
)
HEADERS = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept-Language': 'es-CO,es;q=0.9',
}


class IDMModule(BaseModule):
    """Provide a function to consult the data of
    a user on IDM.
    """

    def _authenticate(self) -> None:
        """Perform the login.

        Raises
        ------
        AutomationError
            If login was not successful.
        AutomationError
            If credentials are not valid.

        """
        data = {
            'username': str(config.IDM_USER),
            'password': str(config.IDM_PASSWORD),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=data)
        if not response.ok:
            raise AutomationError(
                'No se pudo iniciar sesión.',
                detail=response.text,
            )
        if 'selectMenu' not in response.text:
            raise AutomationError(
                'Las credenciales de acceso no son válidas.',
                detail=response.text,
            )

    def _get_user_query_form(
        self,
        form_name: Literal['ConsultarUserEmpleadoSF', 'ConsultarUserTerceros'],
        username: str,
    ) -> FormData:
        """Get the form to find an employee or a third party user.

        Parameters
        ----------
        form_name : Literal['ConsultarUserEmpleadoSF', 'ConsultarUserTerceros']
            Form name, whether employees query form
            or third party users query form.
        username : str
            Username to consult.

        Returns
        -------
        FormData
            Form to find a user.

        Raises
        ------
        AutomationError
            If form could not be obtained.
        AutomationError
            If CSRF token of form was not found.

        """
        params = {'task.tag': form_name}
        response = self.session.get(APP_URL, params=params)
        if not response.ok:
            raise AutomationError(
                f'No se pudo obtener el formulario {form_name}.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            error_message=f'No se encontró el formulario {form_name}.',
        )
        csrf_token = form_data.data.get('OWASP_CSRFTOKEN')
        if not csrf_token:
            raise AutomationError(
                f'No se pudo encontró el token CSRF {form_name}.',
                detail=response.text,
            )
        form_data.data = {
            'OWASP_CSRFTOKEN': csrf_token,
            'ScrollPosX': 0,
            'ScrollPosY': 0,
            'Filter.0.Field': '%LOGIN_ID%',
            'Filter.0.Op': 'EQUALS',
            'Filter.0.Value': '*' + username,
            'Filter.qfrows': 1,
            'main_SUBMIT': 1,
            'action.standardsearch.search': 0,
        }
        return form_data

    def _send_user_query_form(self, user_query_form: FormData) -> str:
        """Send the user query form to find the user.

        Parameters
        ----------
        user_query_form : FormData
            Form to find the user.

        Returns
        -------
        str
            User search result.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        response = self.session.request(
            method=user_query_form.method,
            url=BASE_PLATFORM_URL % user_query_form.action,
            headers=HEADERS,
            data=user_query_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _validate_user(self, user_search_result: str, username: str) -> bool:
        """Check if the user exists.

        Parameters
        ----------
        user_search_result : str
            User search result.
        username : str
            Consulted username.

        Returns
        -------
        bool
            Whether user exists.

        """
        if 'Sin resultados' in user_search_result:
            return False
        soup = get_html_soup(user_search_result)
        user_data_row = soup.find('tr', {'class': 'wsui-table-row-even'})
        if not user_data_row:
            return False
        if username.upper() not in user_data_row.text.upper():
            return False
        return True

    def _fetch_user_data(self, user_query_form: FormData) -> str:
        """Fetch the data of the consulted user.

        Parameters
        ----------
        user_query_form : FormData
            User query form.

        Returns
        -------
        str
            Response containing the data of the consulted user.

        Raises
        ------
        AutomationError
            If data of the user could not be consulted.

        """
        user_query_form.data.pop('action.standardsearch.search', None)
        user_query_form.data['DefaultUserSearchV1__sel'] = 0
        user_query_form.data['action.standardsearch.select'] = 1
        response = self.session.request(
            method=user_query_form.method,
            url=BASE_PLATFORM_URL % user_query_form.action,
            headers=HEADERS,
            data=user_query_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar los datos del usuario.',
                detail=response.text,
            )
        return response.text

    def _extract_field(self, soup: BeautifulSoup, field_id: str) -> str:
        """Extract a field from form.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the form.
        field_id : str
            Field id.

        Returns
        -------
        str
            Field value.

        """
        input_ = soup.find(id=field_id)
        if input_:
            input_value = cast(Tag, input_).get('value', 'N/A')
            if input_value:
                return str(input_value)
        return 'N/A'

    def _extract_user_data(self, user_data_response: str) -> IDMResponseDto:
        """Extract the data of the user.

        Parameters
        ----------
        user_data_response : str
            User data response.

        Returns
        -------
        IDMResponseDto
            Data of the user.

        """
        soup = get_html_soup(user_data_response)
        activation_date = self._extract_field(soup, 'ACTIVATION_DATE')
        expiration_date = self._extract_field(soup, 'EXPIRATION_DATE')
        boss_document = self._extract_field(soup, 'NRO_DOC_IDENTIDAD_JEFE')
        return IDMResponseDto(
            user_type='Tercero',
            activation_date=activation_date,
            expiration_date=expiration_date,
            boss_document=boss_document,
        )

    def _extract_employee_data(
        self,
        user_data_response: str,
    ) -> IDMEmployeeResponseDto:
        """Extract the data of the employee.

        Parameters
        ----------
        user_data_response : str
            User data response.

        Returns
        -------
        IDMEmployeeResponseDto
            Data of the employee.

        """
        soup = get_html_soup(user_data_response)
        user_data = self._extract_user_data(user_data_response)
        employee_status = self._extract_field(soup, 'EMPLOYEE_STATUS')
        payroll_code = self._extract_field(soup, 'C0DIGO_DE_N0MINA')
        business_roles = self._extract_field(soup, 'ROLES_NEGOCIO')
        absenteeism_status = self._extract_field(
            soup, 'TIPOSOLICITUDAUSENTISMO'
        )
        return IDMEmployeeResponseDto(
            user_type='Empleado',
            activation_date=user_data.activation_date,
            expiration_date=user_data.expiration_date,
            boss_document=user_data.boss_document,
            employee_status=employee_status,
            payroll_code=payroll_code,
            absenteeism_status=absenteeism_status,
            roles=business_roles.split('\x1f\x1f'),
        )

    def _create_user_data_dto(
        self,
        user_type: Literal['Empleado', 'Tercero'],
        user_query_form: FormData,
    ) -> IDMResponseDto | IDMEmployeeResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user_type : Literal['Empleado', 'Tercero']
            User type.
        user_query_form : FormData
            Form data containing the results of the query.

        Returns
        -------
        IDMResponseDto | IDMEmployeeResponseDto
            Requested user data.

        """
        user_data_response = self._fetch_user_data(user_query_form)
        if user_type == 'Empleado':
            return self._extract_employee_data(user_data_response)
        return self._extract_user_data(user_data_response)

    def _find_user(
        self,
        username: str,
    ) -> IDMResponseDto | IDMEmployeeResponseDto:
        """Find a user on IDM.

        Parameters
        ----------
        username : str
            Username to be consulted.

        Returns
        -------
        IDMResponseDto | IDMEmployeeResponseDto
            Data of the user/employee.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        user_query_form = self._get_user_query_form(
            'ConsultarUserEmpleadoSF', username
        )
        consulted_user_response = self._send_user_query_form(user_query_form)
        user_exists = self._validate_user(consulted_user_response, username)
        user_type = 'Empleado'
        if not user_exists:
            user_query_form = self._get_user_query_form(
                'ConsultarUserTerceros', username
            )
            consulted_user_response = self._send_user_query_form(
                user_query_form
            )
            user_exists = self._validate_user(
                consulted_user_response, username
            )
            user_type = 'Tercero'
        if not user_exists:
            raise NotFoundError()
        return self._create_user_data_dto(user_type, user_query_form)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(
        self,
        username: str,
    ) -> IDMResponseDto | IDMEmployeeResponseDto:
        """Consult the user by its username
        and get its data.

        Parameters
        ----------
        username : str
            Username to consult.

        Returns
        -------
        IDMResponseDto
            IDM user data.
        IDMEmployeeResponseDto
            IDM employee data.

        """
        try:
            self.create_session()
            self._authenticate()
            return self._find_user(username)
        finally:
            self.close_session()
