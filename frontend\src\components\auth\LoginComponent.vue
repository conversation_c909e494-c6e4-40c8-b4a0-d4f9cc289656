<template>
  <div class="login-container bg-grey-10">
    <q-form class="column q-col-gutter-sm q-pa-xl bg-grey-3 rounded-borders" @submit="onSubmit">
      <div class="text-center">
        <q-img class="logo" src="images/logo.png" alt="Logo SURA" />
      </div>
      <spinner-component v-if="loading" />
      <template v-else>
        <error-list-component :class="{'q-mb-md': form.errors?.common.length}" :errors="form.errors?.common" />
        <field-component v-slot="slotProps" :errors="form.errors?.username">
          <q-input :error="slotProps.hasErrors" v-model="form.data.username" :label="t('username')" dense />
        </field-component>
        <field-component v-slot="slotProps" :errors="form.errors?.password">
          <q-input :error="slotProps.hasErrors" v-model="form.data.password" :label="t('password')" type="password" dense />
        </field-component>
        <div class="text-center">
          <q-btn :label="t('login')" type="submit" color="primary" />
        </div>
      </template>
    </q-form>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { AxiosError } from 'axios';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import UserSession from 'src/lib/userSession';
import loginForm from 'src/forms/auth/loginForm';
import { login } from 'src/services/authService';

import FieldComponent from 'src/components/common/FieldComponent.vue';
import ErrorListComponent from 'src/components/common/ErrorListComponent.vue';
import SpinnerComponent from 'src/components/common/SpinnerComponent.vue';
//#endregion

//#region types
interface LoginEmits {
  (e: 'onLogin', token: string): void;
}
//#endregion

const emits = defineEmits<LoginEmits>();

const { t } = useI18n();

const form = loginForm();

const loading = ref<boolean>(false);

const onSubmit = async (): Promise<void> => {
  try {
    loading.value = true;
    if(!await form.validate()) {
      loading.value = false;
      return;
    }
    const token = await login(form.data);
    emits('onLogin', token);
  } catch (error: unknown) {
    UserSession.clear();
    loading.value = false;
    form.setErrors(error as AxiosError);
  }
}
</script>
