from typing import Generic, Type, TypeVar, Union, Callable
from enum import Enum
from inspect import signature

from lib.base_injectable import BaseInjectable


T = TypeVar('T', bound=BaseInjectable)
InjectableType = Union[Type[T], Callable[[], T]]


class InjectableKind(Enum):
    """Injection methods."""

    SCOPED = 1
    TRANSIENT = 2
    SINGLETON = 3


class InjectableElement(Generic[T]):
    """Object which contains the instance or instances
    of an injectable element.
    """

    element: InjectableType
    kind: InjectableKind
    instance: T | None
    instance_per_request: dict[str, T]

    def __init__(
        self,
        element: InjectableType,
        kind: InjectableKind,
        instance: T | None = None,
    ):
        self.element = element
        self.kind = kind
        self.instance = instance
        self.instance_per_request = {}


class Injectable:
    """Injects an element as a dependency
    storing its instances in order to
    release them depending on the injection
    method used on the injectable element.
    """

    __injectables: dict[Type[BaseInjectable], InjectableElement] = {}

    def add(
        self,
        kind: InjectableKind,
        typeA: Type[T],
        typeB: InjectableType | None = None,
    ):
        """Injects an element.

        Parameters
        ----------
        kind : InjectableKind
            Kind of injection.
        typeA : Type[T]
            Injectable element.
        typeB : InjectableType | None, optional
            Injectable type, by default None.
        """

        if typeB is None:
            self.__injectables[typeA] = InjectableElement(typeA, kind)
        else:
            self.__injectables[typeA] = InjectableElement(typeB, kind)

    def add_scoped(
        self, typeA: Type[T], typeB: InjectableType | None = None
    ) -> None:
        """Injects an element using the SCOPED method.

        Parameters
        ----------
        typeA : Type[T]
            Injectable element.
        typeB : InjectableType | None, optional
            Injectable type, by default None.
        """

        self.add(InjectableKind.SCOPED, typeA, typeB)

    def add_transient(
        self, typeA: Type[T], typeB: InjectableType | None = None
    ) -> None:
        """Injects an element using the TRANSIENT method.

        Parameters
        ----------
        typeA : Type[T]
            Injectable element.
        typeB : InjectableType | None, optional
            Injectable type, by default None.
        """

        self.add(InjectableKind.TRANSIENT, typeA, typeB)

    def add_singleton(
        self, typeA: Type[T], typeB: InjectableType | None = None
    ) -> None:
        """Injects an element using the SINGLETON method.

        Parameters
        ----------
        typeA : Type[T]
            Injectable element.
        typeB : InjectableType | None, optional
            Injectable type, by default None.
        """

        self.add(InjectableKind.SINGLETON, typeA, typeB)

    def end(self):
        """Creates an initial instance for
        the SINGLETON-injected elements.
        """

        for i in self.__injectables.values():
            if i.kind == InjectableKind.SINGLETON:
                i.instance = self.get_element(i)

    def has(self, typeA: Type[T]) -> bool:
        """Checks if an injectable element has
        any instance available.

        Parameters
        ----------
        typeA : Type[T]
            Injectable element.

        Returns
        -------
        bool
            Whether the injectable element
            has any instance available.
        """

        return True if self.__injectables.get(typeA, False) else False

    def get_element(
        self,
        injectable_element: InjectableElement,
        request_id: str | None = None,
    ):
        """Creates an instance of the injected element.

        Parameters
        ----------
        injectable_element : InjectableElement
            Injected element.
        request_id : str | None, optional
            Id of the current request, by default None.

        Returns
        -------
        BaseInjectable
            Injectable element instance.
        """

        element = injectable_element.element
        params = self.get_params(element, request_id)
        if type(element) == Callable:
            return element()
        else:
            return element(*params)  # type: ignore

    def get(self, typeA: Type[T], request_id: str | None = None) -> T | None:
        """Gets an injectable element instance
        based on the injection method.

        Parameters
        ----------
        typeA : Type[T]
            Injectable element.
        request_id : str | None, optional
            Id of the current request, by default None.

        Returns
        -------
        T | None
            Injectable element instance.
        """

        injectable_element = self.__injectables.get(typeA, None)

        if not injectable_element:
            return None

        if (
            injectable_element.instance
            and injectable_element.kind == InjectableKind.SINGLETON
        ):
            return injectable_element.instance

        if request_id and injectable_element.kind == InjectableKind.SCOPED:
            instance = injectable_element.instance_per_request.get(
                request_id, None
            )
            if instance:
                return instance
            instance = self.get_element(injectable_element, request_id)
            injectable_element.instance_per_request[request_id] = instance
            return instance

        if injectable_element.kind == InjectableKind.TRANSIENT:
            return self.get_element(injectable_element)

        injectable_element.instance = self.get_element(injectable_element)
        self.__injectables[typeA] = injectable_element
        return injectable_element.instance

    def clear_scoped_objects(self, request_id: str) -> None:
        """Deletes instances of the SCOPED-injected
        elements for the current request when a response
        has been returned.

        Parameters
        ----------
        request_id : str
            Id of the current request.
        """

        for injectable_element in self.__injectables.values():
            if request_id not in injectable_element.instance_per_request:
                continue
            del injectable_element.instance_per_request[request_id]

    def get_params(
        self, element: InjectableType, request_id: str | None = None
    ) -> list[object]:
        """Gets params of an injectable element
        to check if they are injectables too.
        If so, gets an instance of each param and
        injects them to the passed injectable element.

        Parameters
        ----------
        element : InjectableType
            Injectable element.
        request_id : str | None, optional
            Id of the current request, by default None.

        Returns
        -------
        list[object]
            List of instances to inject in
            the passed injectable element.
        """

        return_params: list[object] = []
        params = signature(element).parameters
        for param in params.values():
            instance = self.get(param.annotation, request_id)
            if instance:
                return_params.append(instance)

        return return_params


injectable = Injectable()
