import json

from dtos.removed_user_dto import RemovedUserResponseDto
from lib import config
from lib.auth import Auth<PERSON><PERSON>, auth
from lib.base_controller import BaseController, get
from lib.responses import (
    DtoResponse,
    ErrorResponse,
    MediaResponse,
    PaginationResponse,
)
from lib.validator.error_validator import ErrorValidator
from services.removed_user_service import RemovedUserService


class RemovedUserController(BaseController):
    """Removed users endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/removed_users')
    async def list(
        self, service: RemovedUserService
    ) -> PaginationResponse[RemovedUserResponseDto] | ErrorResponse:
        """Return a paginated list of removed users.

        Parameters
        ----------
        service : RemovedUserService
            Injected dependency of the removed users service.

        Returns
        -------
        PaginationResponse[RemovedUserResponseDto]
            Paginated list of removed users.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get(
            'limit', config.ROWS_PER_PAGE
        )
        pagination = await service.list(int(page), int(limit), filter_data)
        return PaginationResponse[RemovedUserResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/removed_users/{id:str}')
    async def retrieve(
        self, id: str, service: RemovedUserService
    ) -> DtoResponse[RemovedUserResponseDto] | ErrorResponse:
        """Find a removed user by id.

        Parameters
        ----------
        id : str
            Removed user id.
        service : RemovedUserService
            Injected dependency of the removed users service.

        Returns
        -------
        DtoResponse[RemovedUserResponseDto]
            Removed user details.
        ErrorResponse
            Generic error.

        """
        removed_user = await service.retrieve(id)
        if not removed_user:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido retirado.')
            return ErrorResponse(error, 404)
        return DtoResponse[RemovedUserResponseDto](removed_user)

    @auth(AuthRole.CONSULT)
    @get('/removed_users/username/{username:str}')
    async def retrieve_by_username(
        self, username: str, service: RemovedUserService
    ) -> DtoResponse[RemovedUserResponseDto] | ErrorResponse:
        """Find a removed user by username.

        Parameters
        ----------
        username : str
            Removed user username.
        service : RemovedUserService
            Injected dependency of the removed users service.

        Returns
        -------
        DtoResponse[RemovedUserResponseDto]
            Removed user details.
        ErrorResponse
            Generic error.

        """
        removed_user = await service.retrieve_by_username(username)
        if not removed_user:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido retirado.')
            return ErrorResponse(error, 404)
        return DtoResponse[RemovedUserResponseDto](removed_user)

    @auth(AuthRole.CONSULT)
    @get('/removed_users/{id:str}/export')
    async def export(
        self, id: str, service: RemovedUserService
    ) -> MediaResponse | ErrorResponse:
        """Find a removed user by id and exports its data.

        Parameters
        ----------
        id : str
            Removed user id.
        service : RemovedUserService
            Injected dependency of the removed users service.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.
        """

        file = await service.export(id)
        if not file:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido retirado.')
            return ErrorResponse(error, 404)
        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )

    @auth(AuthRole.CONSULT)
    @get('/removed_users/username/{username:str}/export')
    async def export_by_username(
        self, username: str, service: RemovedUserService
    ) -> MediaResponse | ErrorResponse:
        """Find a removed user by username and exports its data.

        Parameters
        ----------
        username : str
            Removed user username.
        service : RemovedUserService
            Injected dependency of the removed users service.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.

        """
        file = await service.export_by_username(username)
        if not file:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido retirado.')
            return ErrorResponse(error, 404)
        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )
