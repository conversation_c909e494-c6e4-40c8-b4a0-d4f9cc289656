export default {
  //#region common
  appName: 'Consulta y retiro de usuarios',
  footerText: 'ARUS S.A Tecnología + Información + Conocimiento',
  networkError: 'No se pudo establecer la conexión al servidor.',
  anErrorHasOccurred: 'Ha ocurrido un error',
  anErrorHasOccurredWithMessage: 'Ha ocurrido un error: {error}',
  serverIsAvailable: 'El servidor está disponible',
  serverIsNotAvailable: 'El servidor no está disponible',
  pageNotFound: 'Página no encontrada',
  forbidden: 'Permisos insuficientes',
  signInAgainPlease: 'La sesión ha expirado, por favor inicie sesión nuevamente.',
  couldNotSignIn: 'No se pudo iniciar sesión, por favor intente nuevamente.',
  contactToAdmin: 'Si la situación persiste, contacte al administrador.',
  signInAgain: 'Reingresar',
  pleaseWait: 'El proceso puede tardar algunos segundos, por favor espere...',
  noDataLabel: 'No hay datos disponibles',
  return: 'Regresar',
  returnHome: 'Regresar a Inicio',
  darkModeInfo: 'Activar/Desactivar modo oscuro',
  rowsPerPage: 'Filas por página',
  paginationLabel: '{firstRowIndex}-{endRowIndex} de {totalRowsNumber}',
  copyToClipboard: 'Copiar al portapapeles',
  copiedToClipboard: 'Copiado al portapapeles',
  couldNotCopyToClipboard: 'No se pudo copiar al portapapeles',
  changedPassword: 'Contraseña cambiada correctamente',
  currentPassword: 'Contraseña actual',
  changePassword: 'Cambiar contraseña',
  errorUploadFile: 'Error al cargar el archivo.',
  consultLogs: 'Consultar reportes',
  createdBy: 'Creado por',
  updatedBy: 'Actualizado por',
  optional: 'opcional',
  hours: 'Horas',
  minutes: 'Minutos',
  seconds: 'Segundos',
  startTime: 'Fecha y hora de inicio',
  endTime: 'Fecha y hora de fin',
  queryTime: 'Tiempo de la consulta',
  queryResults: 'Resultados de la consulta',
  deletionTime: 'Tiempo del retiro',
  deletionResults: 'Resultados del proceso de retiro',
  groups: 'Grupos',
  home: 'Inicio',
  consult: 'Consultar',
  details: 'Detalles',
  menu: 'Menú',
  expand: 'Expandir',
  condition: 'Condición',
  goBack: 'Regresar',
  load: 'Cargar',
  send: 'Enviar',
  download: 'Descargar',
  save: 'Guardar',
  close: 'Cerrar',
  cancel: 'Cancelar',
  continue: 'Continuar',
  fileFormat: 'Formato del archivo',
  selectAll: 'Seleccionar todo',
  row: 'Fila',
  search: 'Buscar',
  create: 'Crear',
  field: 'Campo',
  clear: 'Limpiar',
  edit: 'Editar',
  delete: 'Eliminar',
  errorMessage: 'Mensaje de error',
  username: 'Usuario',
  password: 'Contraseña',
  fullname: 'Nombre completo',
  loading: 'Cargando',
  creationDate: 'Fecha de creación',
  updateDate: 'Fecha de actualización',
  login: 'Iniciar sesión',
  logout: 'Cerrar sesión',
  platform: 'Plataforma',
  document: 'Número de documento',
  documentType: 'Tipo de documento',
  previousState: 'Estado anterior',
  users: 'Usuarios',
  logs: 'Reportes',
  newState: 'Estado nuevo',
  created: 'Creado',
  updated: 'Actualizado',
  none: 'Ninguna',
  done: 'Hecho',
  success: 'Éxito',
  yes: 'Sí',
  no: 'No',
  new: 'Nuevo',
  correct: 'Correcto',
  error: 'Error',
  warning: 'Advertencia',
  reason: 'Motivo',
  active: 'Activo',
  inactive: 'Inactivo',
  enabled: 'Habilitado',
  frozen: 'Congelado',
  message: 'Mensaje',
  date: 'Fecha',
  see: 'Ver',
  status: 'Estado',
  applications: 'Aplicaciones',
  product: 'Producto',
  code: 'Código',
  description: 'Descripción',
  advancedSearch: 'Búsqueda avanzada',
  export: 'Exportar',
  exportResults: 'Exportar resultados',
  showingRowsNumber: 'Mostrando registros del {startNumber} al {endNumber}. Total: {rowsNumber}',
  result: 'Resultado',
  //#endregion

  //#region validators
  invalidCellPhoneNumber: 'Número de celular inválido.',
  invalidPhoneNumber: 'Número de teléfono inválido.',
  invalidValue: 'Valor inválido.',
  invalidEmail: 'Email inválido.',
  invalidPassword: 'La contraseña no es válida, debe tener al menos {length} caracteres incluyendo mayúsculas, minúsculas, números y caracteres especiales, por ejemplo: !#$%&.\'*+/=?^_`|~-',
  invalidIdCardNumber: 'Número de identificación inválido, debe tener de 6 a 10 dígitos.',
  valueNotFound: 'Valor no encontrado.',
  notNumericalValue: 'No es un valor numérico.',
  valueMustBeGet: 'El valor debe ser mayor o igual que',
  valueMustBeLet: 'El valor debe ser menor o igual que',
  valueMustBeGt: 'El valor debe ser mayor que',
  valueMustBeLt: 'El valor debe ser menor que',
  valueMustBeBetween: 'El valor debe estar entre {number1} y {number2}.',
  valueMustHaveBetween: 'El valor debe tener entre {number1} y {number2} caracteres.',
  valueMustHaveMax: 'El valor debe tener máximo {number} caracteres.',
  valueMustHaveMin: 'El valor debe tener mínimo {number} caracteres.',
  theFieldIncorrect: 'El campo {fieldIncorrect} es incorrecto.',
  isRequired: 'Este campo es obligatorio.',
  //#endregion

  //#region lib
  dataExportNotAvailable: 'La exportación de datos no está disponible.',
  noDataToExport: 'No hay datos para exportar.',
  dataCouldNotBeExported: 'No se pudieron exportar los datos.',
  //#endregion

  //#region fileComponent
  uploadFile: 'Cargar archivo',
  unableUploadFile: 'No es posible cargar el archivo.',
  maxSizeAllowed: 'Tamaño máximo permitido (mb)',
  maxSizeAllowedIs: 'El tamaño máximo permitido es de {size}.',
  maxSizeAllowedExceeded: 'El archivo supera el tamaño máximo permitido.',
  confirmUpload: '¿Estás seguro de querer cargar este archivo?',
  fileTypeNotAllowed: 'Tipo de archivo no permitido',
  //#endregion

  //#region modals
  defaultValueSuccess: 'Se realizó la acción satisfactoriamente.',
  defaultValueError: 'Error al realizar la acción.',
  defaultValueWarning: 'Posible error al realizar la acción.',
  defaultValueInfo: 'Información acerca de la acción realizada.',
  messageModal: '¿Estás seguro de ejecutar esta acción?',
  //#endregion

  //#region exportDataComponent
  exportData: 'Exportar los registros de la tabla',
  enterExportParams: 'Ingrese el número del primer registro, la cantidad de registros que desea exportar y la extensión del archivo.',
  numberOfRecords: 'Cantidad de registros',
  initialIndex: 'Índice inicial',
  enableExport: 'Habilitar la exportación de datos',
  limitRecords: 'Límite de registros',
  fileExtension: 'Extensión del archivo',
  exportAll: 'Exportar todo',
  exportingData: 'Exportando datos, el archivo se descargará en unos segundos...',
  //#endregion

  //#region searchComponent
  contains: 'Contiene',
  equal: 'Igual que',
  notEqual: 'Distinto que',
  greaterThan: 'Mayor que',
  lowerThan: 'Menor que',
  greaterEqualThan: 'Mayor o igual que',
  lowerEqualThan: 'Menor o igual que',
  useFiltersToSearch: 'Utilizar filtros de búsqueda',
  //#endregion

  //#region users
  usernameCannotBeChanged: 'Una vez creado el usuario, no se puede cambiar el nombre de usuario.',
  passwordUploadUser: 'Al crear usuarios de forma masiva la contraseña por defecto será el número de documento.',
  uploadUsers: 'Cargar usuarios',
  firstName: 'Nombres',
  moduleUsername: 'Usuario Módulo',
  lastName: 'Apellidos',
  role: 'Rol',
  email: 'Correo electrónico',
  idCard: 'Cédula',
  cellPhone: 'Número de celular',
  confirmPassword: 'Confirmar contraseña',
  errorDeleteUser: 'No se pudo eliminar al usuario.',
  errorLoadUser: 'Error al cargar al usuario.',
  confirmDeleteUser: '¿Estás seguro de querer eliminar este usuario?',
  errorLoadUsers: 'Error cargando usuarios.',
  errorConfirmPassword: 'Las contraseñas no coinciden',
  createUser: 'Crear usuario',
  editUser: 'Editar usuario',
  userList: 'Usuarios',
  retrieveUser: 'Ver usuario',
  must_answer_all_questions: 'Debe responder todas las preguntas',
  manageUsers: 'Administrar usuarios',
  descriptionListUser: 'Esta sección permite visualizar, editar y eliminar los usuarios que se encuentran registrados en la aplicación.',
  passwordConditions: 'Debe tener tener al menos {length} caracteres incluyendo mayúsculas, minúsculas, números y caracteres especiales: {characters}',
  generateRandomPassword: 'Generar contraseña aleatoria',
  passwordLength: 'Longitud mínima de la contraseña',
  passwordSpecialCharacters: 'Caracteres especiales de la contraseña',
  //#endregion

  //#region automation
  transaction: 'ID de transacción',
  runAutomation: 'Ejecutar automatización',
  descriptionAutomation: 'En esta sección puedes cargar el archivo con las cédulas para ejecutar la automatización.',
  downloadFormat: 'Descargar formato',
  formatShouldBeOneOfThese: 'El formato debe ser de alguna de las siguientes extensiones:',
  canDownloadOrCreateAFormat: 'Puedes descargar el formato predeterminado o puedes crear tu propio formato personalizado.',
  columnsOrderShouldBeAsFollows: 'El orden de las columnas del archivo debe ser el siguiente:',
  firstRowMWillNotBeConsidered: 'La primera fila del archivo no será tomada en cuenta. Puede usarla para nombrar las columnas.',
  validationErrorsInFile: 'Se encontraron errores de validación en el archivo. Por favor solucione los errores e intente cargar el archivo nuevamente.',
  errorListFound: 'Errores encontrados:',
  downloadResultsFile: 'Descargar resultados',
  downloadingResultsFile: 'Descargando resultados, esto puede tomar unos segundos...',
  downloadingDailyResultsFile: 'Descargando resultados diarios, esto puede tomar unos minutos...',
  lastDayDeletions: 'Retiros del día anterior',
  exportDailyReport: 'Exportar reporte diario',
  exportDailyReportTooltip: 'Exportar consultas y/o retiros realizadas durante el día de hoy o un consolidado de los retiros del día anterior.',
  exportDailyReportTimeWarning: 'Este proceso de exportación puede tomar algunos minutos, especialmente si se realizaron más de 20 operaciones de consulta o retiro.',
  selectWhatToExport: 'Por favor seleccione lo que desea exportar',
  forConsultResults: 'Para los resultados de consulta:',
  onlyAppsWhichUserHasPrivilegesOn: 'Sólo las aplicaciones donde el usuario tiene privilegios',
  includeAppsWhichUserWasNotFoundOn: 'Incluir aplicaciones en las que el usuario no fue encontrado.',
  includeErrorResults: 'Incluir aplicaciones que presentaron errores.',
  consultedResults: 'Resultados de consulta',
  removedResults: 'Resultados de retiro',
  allResults: 'Todos los resultados',
  anAutomationErrorHasOccurred: 'Ha ocurrido un error desconocido durante la automatización.',
  automationSuccessfully: 'Automatización ejecutada correctamente',
  validDocumentTypesText: 'El tipo de documento debe ser exactamente igual a uno de los siguientes:',
  consultUser: 'Consultar usuario',
  consultUsers: 'Consultar usuarios',
  removeUser: 'Retirar usuario',
  removeUsers: 'Retirar usuarios',
  startRemovingUsers: 'Iniciar retiro',
  removePermissions: 'Eliminar permisos',
  removePermissionsInfo: 'Retirar los usuarios en las aplicaciones seleccionadas',
  catalogNumber: 'Número de catálogo',
  catalogNumberInputInfo: 'Este número de catálogo aplica a todos los usuarios. Si un usuario tiene un número de catálogo diferente, puede especificarlo en la tabla de abajo en la columna "Número de catálogo".',
  showConsultResults: 'Mostrar resultados de consulta',
  showRemoveResults: 'Mostrar resultados de retiro',
  removeUserOnThisApp: 'Retirar usuario en esta aplicación',
  youAreAboutToRemoveUsers: 'Está apunto de iniciar el proceso de retiro de los usuarios en las aplicaciones seleccionadas.',
  areYouSureYouWantToRemoveUsers: '¿Confirma que desea retirar los usuarios en las aplicaciones seleccionadas?',
  removeUsersConfirmation: 'Confirmo que deseo iniciar el retiro de los usuarios.',
  usersToConsult: 'Usuarios a consultar',
  uploadUsersToConsultFile: 'Cargar archivo con los usuarios a consultar',
  uploadUsersToConsultFileInfo: 'Ingrese los usuarios en la primera columna del archivo.',
  separateUsersByComma: 'Ingrese los usuarios separados por coma (,)',
  consultOtherUsers: 'Consultar otros usuarios',
  consultedUsers: 'Usuarios consultados',
  removedUsers: 'Usuarios retirados',
  usersToRemove: 'Usuarios a retirar',
  applicationsCount: 'Cantidad de aplicaciones',
  applicationsWithWarning: 'Aplicaciones con advertencia',
  applicationsWithError: 'Aplicaciones con error',
  queriesTime: 'Tiempo de todas las consultas',
  queryStartDate: 'Fecha y hora de inicio de la consulta',
  queryEndDate: 'Fecha y hora de fin de la consulta',
  userDeletionTime: 'Tiempo de todos los retiros',
  userDeletionStartDate: 'Fecha y hora de inicio del retiro',
  userDeletionEndDate: 'Fecha y hora de fin del retiro',
  inThisSpaceYouCanSeeTheResult: 'En este espacio se podrá visualizar el resultado de las consulta o retiros de los usuarios',
  clickOnTheConsultUsersButton: 'Para realizar la consulta de usuarios, haga click en el botón',
  clickOnTheExportDailyReportButton: 'Para generar un reporte de las consultas y/o retiros realizados el día de hoy, o un consolidado de los retiros del dia anterior, haga click en el botón',
  youCanRemoveUsersAfterConsultingThem: 'Al finalizar la consulta, podrá realizar el retiro de los usuarios en algunas aplicaciones.',
  locatedInTheTopLeftCorner: 'ubicado en la esquina superior izquierda.',
  locatedInTheTopRightCorner: 'ubicado en la esquina superior derecha.',
  selectHowToConsultUsers: 'Seleccione la forma en la que desea consultar los usuarios',
  inputUsersToConsult: 'Ingresar los usuarios a consultar',
  loadFileWithUsersToConsult: 'Cargar archivo de Excel o CSV',
  successMessage: 'Mensaje de éxito',
  userNotFound: 'Usuario no encontrado',
  userCouldNotBeConsulted: 'El usuario no pudo ser consultado en esta aplicación.',
  userCouldNotBeConsultedBecauseOfMissingData: 'El usuario no pudo ser consultado en esta aplicación debido a datos faltantes.',
  applicationWasIgnored: 'Se omitió la consulta del usuario en esta aplicación.',
  multipleUsersFound: 'Múltiples usuarios encontrados',
  userProfileNotFound: 'Perfil no encontrado',
  userLicenseNotFound: 'Licencia no encontrada',
  userLicenseUnauthorized: 'Licencia no válida',
  manuallyDeletionAppsText: 'Las siguientes aplicaciones no están integradas con la funcionalidad de retiro, por lo tanto, se debe retirar el usuario manualmente: {apps}.',
  showLastActionResults: 'Mostrar resultados de la última acción',
  wantToSeeTheLastActionResults: '¿Desea ver los resultados de la última consulta o retiro?',
  lastActionResultsNotFound: 'No se encontraron los resultados de la última acción. Por favor realice la consulta nuevamente.',
  //#endregion

  //#region automation responses
  catalog_number: 'Número de catálogo',
  roles: 'Roles',
  id: 'ID',
  branch: 'Sucursal',
  position: 'Cargo',
  name: 'Nombre rol',
  channels: 'Canales',
  member_of: 'Grupos',
  authorities: 'Aseguradores de autoridad',
  locked: 'Bloqueado',
  sam_account_name: 'Nombre de usuario',
  first_name: 'Nombres',
  last_name: 'Apellidos',
  mail: 'Correo electrónico',
  profile: 'Perfil',
  profiles: 'Perfiles',
  license: 'Licencia',
  licenses: 'Licencias',
  removed_licenses: 'Licencias retiradas',
  company: 'Compañía',
  user_principal_name:'Usuario de Azure',
  discharge_date: 'Fecha de alta',
  leaving_date: 'Fecha de baja',
  category: 'Categoría',
  permission_codes: 'Códigos de permiso',
  branches: 'Ramos',
  offices: 'Oficinas',
  cas: 'CAS',
  user_type: 'Tipo de usuario',
  activation_date: 'Fecha de activación',
  expiration_date: 'Fecha de retiro',
  boss_document: 'Documento del jefe',
  employee_status: 'Estado del empleado',
  payroll_code: 'Código de nómina',
  absenteeism_status: 'Estado de ausentismo',
  repository: 'Repositorio',
  removed_groups: 'Grupos retirados',
  removed_role: 'Rol retirado',
  removed_roles: 'Roles retirados',
  unremoved_roles: 'Roles no retirados'
  //#endregion
};
