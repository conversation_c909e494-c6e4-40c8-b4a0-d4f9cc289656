//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class RangeLengthValidator extends ClassValidator {

  private startNumber: number;
  private endNumber: number;
  private includeLimits: boolean;

  public constructor(value: unknown, startNumber: number, endNumber: number, includeLimits: boolean, message?: string) {
    super(value);
    this.includeLimits = includeLimits;
    this.startNumber = startNumber;
    this.endNumber = endNumber;
    this.message = (message) ? message : '';
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    if(!value) return true;
    if(this.includeLimits) {
      if(value.length >= this.startNumber && value.length <= this.endNumber) return this.isValid = true;
    }
    if(value.length > this.startNumber && value.length < this.endNumber) return this.isValid = true;
    this.message = this.message ? this.message : `${i18n.global.t('valueMustHaveBetween', (this.includeLimits ? { number1: this.startNumber, number2: this.endNumber } : { number1: this.startNumber+1, number2: this.endNumber-1 }))}`;
    return this.isValid = false;
  }
}

export default RangeLengthValidator;