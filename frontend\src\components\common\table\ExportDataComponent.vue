<template>
  <q-btn class="text-caption q-px-sm" dense size="sm" color="primary" :icon="props.icon" :label="props.label" @click="showDialog">
    <q-tooltip class="text-body2" :offset="[10, 10]">
      {{ t('export') }}
    </q-tooltip>
  </q-btn>
  <modal-component
    v-model="show"
    icon="download"
    confirm-modal
    :title="t('exportData')"
    :message="t('enterExportParams')"
    :continue-action-label="t('export')"
    @on-continue="onTrue"
    @on-cancel="() => show = false">
    <template #body>
      <q-card-section class="q-pt-md q-ml-sm">
        <span class="text-body1">
          {{t('initialIndex')}}
        </span>
        <q-input
          v-model="exportParams.startIndex"
          type="number"
          min="1"
          :max="props.recordsNumber"
          dense />
      </q-card-section>
      <q-card-section class="q-pt-none q-ml-sm">
        <span class="text-body1">{{t('numberOfRecords')}}</span>
        <template v-if="props.recordsNumber != undefined && exportParams.limit != props.recordsNumber">
          &nbsp;<span class="text-caption cursor-pointer text-primary" @click="exportAll">{{t('exportAll')}}</span>
        </template>
        <q-input
          v-model="exportParams.limit"
          type="number"
          min="1"
          :max="props.recordsNumber"
          dense />
      </q-card-section>
      <q-card-section class="q-pt-none q-ml-sm">
        <span class="text-body1">{{t('fileExtension')}}</span>
        <q-select
          v-model="exportParams.extension"
          :options="extensionOptions"
          map-options
          emit-value
          dense />
      </q-card-section>
    </template>
  </modal-component>
</template>

<script setup lang="ts">
//#region imports
import { QSpinnerGears, useQuasar } from 'quasar';
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import type { Search, ExportAction, SelectOption } from 'src/lib/interfaces';

import ModalComponent from 'src/components/common/ModalComponent.vue';
//#endregion

//#region types
interface ExportDataProps {
  exportAction: ExportAction;
  recordsNumber?: number;
  icon?: string;
  label?: string;
  filter?: Search;
}

interface ExportParams {
  extension: 'csv' | 'xlsx' | 'json';
  startIndex: number;
  limit: number;
}
//#endregion

const props = defineProps<ExportDataProps>();

const show = ref<boolean>(false);

const $q = useQuasar();

const { t } = useI18n();

const exportParams = reactive<ExportParams>({
  extension: 'xlsx',
  startIndex: 1,
  limit: props.recordsNumber ?? 0
});

const extensionOptions: SelectOption[] = [
  {label: 'Excel (xlsx)', value: 'xlsx'},
  {label: 'CSV', value: 'csv'},
  {label: 'JSON', value: 'json'},
];

const exportAll = (): void => {
  exportParams.limit = props.recordsNumber ?? 0;
}

const showDialog = (): void => {
  exportParams.limit = props.recordsNumber ?? 0;
  show.value = true;
}

const onTrue = async (): Promise<void> => {
  show.value = false;
  $q.notify({
    spinner: QSpinnerGears,
    message: t('exportingData'),
    color: 'positive',
    position: 'top',
  });
  await props.exportAction(exportParams.extension, exportParams.startIndex-1, exportParams.limit, props.filter);
}
</script>
