import re
from typing import cast

from bs4 import BeautifulSoup, ResultSet, Tag

from dtos.automation_dto import STARResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import get_html_soup, retry, strip_text

APP_URL = 'https://appsseg.suranet.com/star2/InicioServlet'
BASE_PLATFORM_URL = 'https://appsseg.suranet.com/star2/%s'
USER_NOT_FOUND_MESSAGES = [
    'No se han creado funcionarios',
    'Datos no disponibles',
]
USERS_FORM = 'AdmonFuncionariosServlet'


class STARModule(BaseModule):
    """Provides a function to consult the roles
    of a user on STAR.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(APP_URL)

    def _filter_found_documents(self, rows: ResultSet) -> str | None:
        """Return the document which starts with alpha characters
        if two or more are found, otherwise, return the first one.

        Parameters
        ----------
        rows : ResultSet
            Rows containing the found users
            basic information.

        Returns
        -------
        str | None
            Document of the user.

        """
        found_documents = []
        for row in rows[1:]:
            found_documents.append(strip_text(row.td.text))
        if not found_documents:
            return None
        if len(found_documents) > 1:
            return next(
                filter(
                    lambda found_document: re.match('[a-zA-Z]', found_document)
                    is not None,
                    found_documents,
                ),
                None,
            )
        return found_documents[0]

    def _extract_user_document(self, filtered_users_response: str) -> str:
        """Extract the user's document.

        Parameters
        ----------
        filtered_users_response : str
            HTML Response containing the filtered users.

        Returns
        -------
        str
            Document of the user.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        if any(
            message in filtered_users_response
            for message in USER_NOT_FOUND_MESSAGES
        ):
            raise NotFoundError()

        soup = get_html_soup(filtered_users_response)
        if not soup.form or not soup.form.table:
            raise NotFoundError()
        rows = soup.form.table.find_all('tr')
        document = self._filter_found_documents(rows)
        if not document:
            raise NotFoundError()
        return document

    def _get_user_document(self, fullname: str) -> str:
        """Get the user's document.

        Parameters
        ----------
        fullname : str
            Fullname to consult.

        Returns
        -------
        str
            Document of the user.

        Raises
        ------
        AutomationError
            If users could not be filtered.

        """
        data = {'accion': 'listarFuncionarios', 'txtNombre': fullname}
        response = self.session.post(BASE_PLATFORM_URL % USERS_FORM, data=data)
        if not response.ok:
            raise AutomationError(
                'No se pudo filtrar los usuarios.',
                detail=response.text,
            )
        return self._extract_user_document(response.text)

    def _extract_select_field_value(
        self,
        soup: BeautifulSoup,
        field_name: str,
    ) -> str:
        """Extract the value of a select field.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user information.
        field_name : str
            Field name.

        Returns
        -------
        str
            Value of the field.

        """
        element = soup.find('select', {'name': field_name})
        if element:
            selected = cast(Tag, element).find('option', {'selected': True})
            if selected:
                return strip_text(selected.text)
        return 'N/A'

    def _extract_input_field_value(
        self,
        soup: BeautifulSoup,
        field_name: str,
    ) -> str:
        """Extract the value of a input field.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user information.
        field_name : str
            Field name.

        Returns
        -------
        str
            Value of the field.

        """
        default = 'N/A'
        element = soup.find('input', {'name': field_name})
        if element:
            return str(cast(Tag, element).get('value', default))
        return default

    def _extract_user_data(self, user_data_response: str) -> STARResponseDto:
        """Extract the data of the user.

        Parameters
        ----------
        user_data_response : str
            User data response content.

        Returns
        -------
        STARResponseDto
            Data of the user.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        if any(
            message in user_data_response
            for message in USER_NOT_FOUND_MESSAGES
        ):
            raise NotFoundError()

        soup = get_html_soup(user_data_response)
        cas = self._extract_select_field_value(soup, 'slcCdCas')
        role = self._extract_select_field_value(soup, 'slcCdRol')
        discharge_date = self._extract_input_field_value(soup, 'txtFechaAlta')
        leaving_date = self._extract_input_field_value(soup, 'txtFechaBaja')
        return STARResponseDto(
            cas=cas,
            role=role,
            discharge_date=discharge_date,
            leaving_date=leaving_date,
        )

    def _send_consult_user_form(self, fullname: str, document: str) -> STARResponseDto:
        """Consult a user and return its data.

        Parameters
        ----------
        fullname : str
            User's fullname.
        document : str
            User's document.

        Returns
        -------
        STARResponseDto
            Requested user data.

        Raises
        ------
        AutomationError
            If user data could not be consulted.

        """
        data = {
            'paginaActual': '1',
            'accion': 'verFuncionario',
            'hdnCdDni': document,
            'txtNombre': fullname,
        }
        response = self.session.post(BASE_PLATFORM_URL % USERS_FORM, data=data)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar los datos del usuario.',
                detail=response.text,
            )
        return self._extract_user_data(response.text)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, fullname: str) -> STARResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        fullname : str
            Fullname of the user to consult.

        Returns
        -------
        STARResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            document = self._get_user_document(fullname)
            return self._send_consult_user_form(fullname, document)
        finally:
            self.close_session()
