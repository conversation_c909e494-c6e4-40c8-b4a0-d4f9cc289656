from threading import Lock


class SingletonMeta(type):
    """Thread-safe implementation of Singleton.

    Usage:

    >>> class MyClass(metaclass=SingletonMeta):
    >>>     ...
    >>> my_instance = MyClass()
    >>> my_other_instance = MyClass()
    >>> assert my_instance is my_other_instance
    """

    _instances = {}
    _lock = Lock()

    def __call__(cls, *args, **kwargs):
        with cls._lock:
            if cls not in cls._instances:
                instance = super().__call__(*args, **kwargs)
                cls._instances[cls] = instance
        return cls._instances[cls]


class Singleton(metaclass=SingletonMeta):
    """Inherit from this class to make a thread-safe singleton."""

    pass
