import json
from typing import Any

from dtos.automation_dto import BillingCenterResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import filter_dict_list_by_field, retry

APP_URL = 'https://coreseguros.suramericana.com/bc/BillingCenter.do'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'}
ACTIVE_FIELD_ID = 'UserDetailPage:UserDetailScreen:UserDetailDV:Active'
LOCKED_FIELD_ID = 'UserDetailPage:UserDetailScreen:UserDetailDV:AccountLocked'
FUNCTIONS_FIELD_ID = 'UserDetailPage:UserDetailScreen:UserDetailDV:RolesLVI'


class BillingCenterModule(BaseModule):
    """Provide a function to consult the data of
    a user on Billing Center.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(APP_URL)

    def _get_main_page(self) -> None:
        """Get the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )

    def _get_user_query_form(self) -> None:
        """Get the user query form.

        Raises
        ------
        AutomationError
            If user query form could not be fetched.

        """
        payload = {
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'TabBar:AdministrationTab:Admin_UsersAndSecurity'
                ':UsersAndSecurity_UserSearch_act'
            ),
            'objFocusId': ':tabs-menu-trigger',
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta de usuarios.',
                detail=response.text,
            )

    def _send_consult_user_form(self, username: str) -> None:
        """Consult the user by its username.

        Parameters
        ----------
        username : str
            Username of the user to consult.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        payload = {
            'UserSearch:UserSearchScreen:UserSearchDV:UsernameCriterion': username,
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'UserSearch:UserSearchScreen:UserSearchDV:'
                'SearchAndResetInputSet:SearchLinksInputSet:Search_act'
            ),
            'objFocusId': (
                'UserSearch:UserSearchScreen:UserSearchDV:'
                'SearchAndResetInputSet:SearchLinksInputSet:Search'
            ),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                f'No se pudo consultar al usuario "{username}".',
                detail=response.text,
            )

    def _fetch_user_data(self, username: str) -> Any:
        """Get the data of the user.

        Parameters
        ----------
        username : str
            Username of the consulted user.

        Returns
        -------
        Any
            User data.

        Raises
        ------
        AutomationError
            If user data could not be obtained.

        """
        payload = {
            'UserSearch:UserSearchScreen:UserSearchDV:UsernameCriterion': username,
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': 'UserSearch:UserSearchScreen:UserSearchResultsLV:0:DisplayName_act',
            'objFocusId': 'UserSearch:UserSearchScreen:UserSearchResultsLV:0:DisplayName',
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                f'No se pudo obtener los datos del usuario "{username}".',
                detail=response.text,
            )
        return response.json()

    def _extract_text_field(
        self,
        user_data_fields: list[dict[str, Any]],
        field_value: Any,
        field_name: str = 'id',
    ) -> str | None:
        """Extract the value of a text field.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.
        field_value : Any
            Value of the field to extract.
        field_name : str, optional
            Name of the field to extract, by default 'id'.

        Returns
        -------
        str | None
            Value of the field if found.

        """
        field = filter_dict_list_by_field(
            user_data_fields, field_name, field_value
        )
        return field.get('editValue')

    def _extract_active_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> bool:
        """Extract the active state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is active.

        Raises
        ------
        AutomationError
            If field not found.

        """
        active = self._extract_text_field(user_data_fields, ACTIVE_FIELD_ID)
        if active is None:
            raise AutomationError(
                'No se encontró el campo "Activo" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return active == 'true'

    def _extract_locked_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> bool:
        """Extract the locked state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is locked.

        Raises
        ------
        AutomationError
            If field not found.

        """
        locked = self._extract_text_field(user_data_fields, LOCKED_FIELD_ID)
        if locked is None:
            raise AutomationError(
                'No se encontró el campo "Bloqueado" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return locked == 'true'

    def _extract_roles_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> list[str]:
        """Extract the roles of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        list[str]
            Roles of the user.

        Raises
        ------
        AutomationError
            If functions could not be extracted.

        """
        field = filter_dict_list_by_field(
            user_data_fields, 'id', FUNCTIONS_FIELD_ID
        )
        try:
            user_functions = field['items'][0]['data'].get('root', [])
            return [role['c1']['text'] for role in user_functions]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer las funciones del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            ) from e

    def _extract_user_data_fields(
        self,
        data: Any,
    ) -> list[dict[str, Any]]:
        """Extract the data fields from the user data.

        Parameters
        ----------
        data : Any
            User data.

        Returns
        -------
        list[dict[str, Any]]
            User data fields.

        Raises
        -------
        AutomationError
            If user data fields could not be extracted.
        NotFoundError
            If user was not found.

        """
        try:
            # Fourth "items" key contains the user data if found
            key = 'items'
            fields = data[2][key][0][key][1][key][0].get(key)
            if not fields:
                raise NotFoundError()
            return fields[0][key][0][key]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer los datos del usuario.',
                detail=json.dumps(data, ensure_ascii=False),
            ) from e

    def _create_user_data_dto(
        self,
        user_data: Any,
    ) -> BillingCenterResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_data : Any
            User data object.

        Returns
        -------
        BillingCenterResponseDto
            Requested user data.

        """
        user_data_fields = self._extract_user_data_fields(user_data)
        return BillingCenterResponseDto(
            active=self._extract_active_field(user_data_fields),
            locked=self._extract_locked_field(user_data_fields),
            roles=self._extract_roles_field(user_data_fields),
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> BillingCenterResponseDto:
        """Consult the user by its username and get its data.

        Parameters
        ----------
        username : str
            Username to consult.

        Returns
        -------
        BillingCenterResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            self._get_main_page()
            self._get_user_query_form()
            self._send_consult_user_form(username)
            user_data = self._fetch_user_data(username)
            return self._create_user_data_dto(user_data)
        finally:
            self.close_session()
