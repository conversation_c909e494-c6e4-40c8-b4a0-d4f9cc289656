<template>
  <div class="fullscreen bg-blue text-white text-center q-pa-md flex flex-center">
    <div>
      <div style="font-size: 30vh">
        404
      </div>

      <div class="text-h2" style="opacity:.4">{{t('pageNotFound')}}</div>

      <q-btn
        class="q-mt-xl"
        color="white"
        text-color="blue"
        unelevated
        to="/"
        :label="t('returnHome')"
        no-caps
      />
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useI18n } from 'vue-i18n';
//#endregion

const { t } = useI18n();
</script>
