from typing import Any

from lib import config
from lib.salesforce import Salesforce
from modules.salesforce_module import SalesforceModule

RETIRED_MANAGER_STATE = 7


class HealthCloudModule(SalesforceModule):
    """Provide functions to consult and remove
    a user on Health Cloud.
    """

    def __init__(self, sf: Salesforce) -> None:
        super().__init__(sf)
        self.module = 'Health Cloud'

    def _connect(self, use_retiros_user: bool = False) -> None:
        if use_retiros_user:
            username = config.HEALTH_CLOUD_RETIROS_USER
            password = config.HEALTH_CLOUD_RETIROS_PASSWORD
            security_token = config.HEALTH_CLOUD_RETIROS_SECURITY_TOKEN
        else:
            username = config.HEALTH_CLOUD_USER
            password = config.HEALTH_CLOUD_PASSWORD
            security_token = config.HEALTH_CLOUD_SECURITY_TOKEN
        self.salesforce.connect(
            domain=config.HEALTH_CLOUD_DOMAIN,
            username=str(username),
            password=str(password),
            security_token=str(security_token),
            client_id=str(config.HEALTH_CLOUD_CLIENT_ID),
            client_secret=str(config.HEALTH_CLOUD_CLIENT_SECRET),
        )

    def _get_disable_user_fields(self) -> dict[str, Any]:
        fields = super()._get_disable_user_fields()
        fields.update(
            {
                'IsActive': False,
                'PHM_EstadoGestor__c': RETIRED_MANAGER_STATE,
            }
        )
        return fields
