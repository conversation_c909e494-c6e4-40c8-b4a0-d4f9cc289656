//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class IdCardValidator extends ClassValidator {

  public constructor(value: unknown, message?: string) {
    super(value);
    this.message = (message) ? message : i18n.global.t('invalidIdCardNumber');
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    const validateFields = /^[0-9]{6,10}$/;
    return this.isValid = (value) ? value.match(validateFields) != null : true;
  }
}

export default IdCardValidator;