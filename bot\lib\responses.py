import typing

import orjson
from starlette import responses
from starlette.background import BackgroundTask
from starlette.responses import FileR<PERSON>ponse, Response

from lib.base_dto import BaseResponseDto
from lib.base_response import BaseResponse
from lib.pagination import P, Pagination
from lib.validator.error_validator import ErrorValidator

T = typing.TypeVar('T', bound=BaseResponseDto | list[BaseResponseDto])


class ORJSONResponse(responses.JSONResponse):
    def render(self, content: typing.Any) -> bytes:
        return orjson.dumps(content)


class DtoResponse(ORJSONResponse, typing.Generic[T]):
    def __init__(
        self,
        dto: T | list[T],
        status_code: int = 200,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        dto_response: dict[str, object] | list[dict[str, object]]
        if isinstance(dto, list):
            dto_response = [element.to_response() for element in dto]  # type: ignore
        else:
            if isinstance(dto, object):
                dto_response = dto.to_response()
        super().__init__(
            dto_response, status_code, headers, media_type, background
        )


class EmptyResponse(BaseResponse):
    def __init__(
        self,
        status_code: int = 200,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        super().__init__(None, status_code, headers, media_type, background)


class PaginationResponse(BaseResponse, typing.Generic[P]):
    def __init__(
        self,
        pagination: Pagination[P],
        status_code: int = 200,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        super().__init__(
            pagination.to_response(),
            status_code,
            headers,
            media_type,
            background,
        )


class MediaResponse(Response):
    def __init__(
        self,
        stream_or_path: bytes | str,
        status_code: int = 200,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        super().__init__(
            stream_or_path, status_code, headers, media_type, background
        )


class ChunkEncodedMediaResponse(MediaResponse, FileResponse):
    def __init__(
        self,
        stream_or_path: bytes | str,
        status_code: int = 200,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        super().__init__(
            stream_or_path, status_code, headers, media_type, background
        )
        FileResponse.__init__(
            self,
            typing.cast(str, stream_or_path),
            status_code,
            headers,
            media_type,
            background,
        )


class ErrorResponse(BaseResponse):
    def __init__(
        self,
        error: ErrorValidator,
        status_code: int = 400,
        headers: typing.Optional[typing.Dict[str, str]] = None,
        media_type: typing.Optional[str] = None,
        background: typing.Optional[BackgroundTask] = None,
    ) -> None:
        super().__init__(
            error.errors, status_code, headers, media_type, background
        )
