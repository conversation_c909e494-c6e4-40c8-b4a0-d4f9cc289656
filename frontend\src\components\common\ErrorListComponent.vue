<template>
  <div>
    <template v-for="(error, index) in props.errors" :key="index">
      <error-component :label="error" :bullet="props.bullet" />
    </template>
  </div>
</template>

<script setup lang="ts">
//#region imports
import ErrorComponent from 'src/components/common/ErrorComponent.vue';
//#endregion

//#region types
interface ErrorListProps {
  errors?: string[];
  bullet?: string;
}
//#endregion

const props = withDefaults(defineProps<ErrorListProps>(), {
  errors: () => []
})
</script>
