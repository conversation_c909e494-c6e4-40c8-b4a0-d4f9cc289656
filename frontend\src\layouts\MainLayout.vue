<template>
  <q-layout view="hHh Lpr lff">
    <q-header elevated>
      <q-toolbar class="row items-center">
        <q-img src="images/logo-sura.png" alt="Logo Sura" class="logo" width="120px" />
        <q-toolbar-title class="col text-center">
          {{ t('appName') }}
        </q-toolbar-title>
        <div class="row items-center justify-end col-auto">
          <q-btn flat round dense :icon="isDarkMode ? 'sunny' : 'brightness_3'" @click="toggleDarkMode">
            <q-tooltip class="text-body2" :offset="[10, 10]">
              {{ t('darkModeInfo') }}
            </q-tooltip>
          </q-btn>
          <div class="col-auto">
            <user-menu-component />
          </div>
        </div>
      </q-toolbar>
    </q-header>
    <q-page-container>
      <router-view />
    </q-page-container>
    <q-footer class="row q-px-xl flex-center" :class="$q.dark.isActive ? 'bg-dark text-white':'bg-white text-dark' " bordered>
      <q-img src="images/arus_logo.png" fit="contain" class="cursor-pointer q-mr-sm" height="50px" width="60px" @click="goToArusPage" />
      <span class="cursor-pointer self-center" @click="goToArusPage"> {{ new Date().getFullYear() }} | {{ t('footerText') }} </span>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
//#region imports
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import UserMenuComponent from 'src/components/layouts/UserMenuComponent.vue';
import { useQuasar } from 'quasar';
//#endregion

const { t } = useI18n({ useScope: 'global' });

const $q = useQuasar();

const isDarkMode = ref<boolean>($q.dark.mode as boolean);

const goToArusPage = (): void => {
  window.open('https://www.arus.com.co/', '_blank');
}

const toggleDarkMode = () => {
  $q.dark.toggle();
  isDarkMode.value = $q.dark.isActive;
  $q.localStorage.set('dark', $q.dark.isActive);
};
</script>
