import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=int | float)


class LowerThanValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, compare_value: int | float, message: str | None = None) -> None:
        super().__init__(value)
        self.__compare_value = compare_value
        self.message = message if message else ''

    async def validate(self) -> bool:
        value = self.value()
        if not value:
            return True

        if isinstance(value, (float, int)):
            self.message = 'must_be_lower_than'
            self.format_values = {'compare_value': self.__compare_value}
            self.is_valid = value < self.__compare_value
            return self.is_valid

        self.message = 'is_not_numeric_value'
        self.is_valid = False
        return self.is_valid
