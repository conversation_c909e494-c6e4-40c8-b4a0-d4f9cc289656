from http.client import UNAUT<PERSON><PERSON><PERSON><PERSON><PERSON>
from urllib.parse import urljoin

import jwt
from asyncify import asyncify
from requests import Response
from requests.exceptions import ConnectionError, Timeout
from starlette.responses import HTMLResponse, RedirectResponse

from lib import config
from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.exceptions import BadRequestError
from lib.http.sessions import TLSSession
from lib.seus import SEUS
from lib.user_session import UserSession


class AuthService(BaseInjectable, BaseService):
    """Service to authenticate users on SEUS 4."""

    def __init__(self) -> None:
        super().__init__()
        self.seus = SEUS(config.SERVICE)

    def _get_auth_server_url(self) -> str:
        return f'{config.PROTOCOL}://auth:3000/'

    def _get_redirection_url(self) -> str:
        return (
            f'{config.PROTOCOL}://{config.SERVER_NAME}:{config.FRONTEND_PORT}/'
        )

    def get_authenticated_response(
        self, user: UserSession
    ) -> RedirectResponse:
        """Get the redirection response for
        a successful authentication.

        Parameters
        ----------
        user : UserSession
            Authenticated user session.

        Returns
        -------
        RedirectResponse
            Response.

        """
        url = self._get_redirection_url()
        response = RedirectResponse(url, status_code=302)
        payload = {
            'session': user.session_token,
            'username': user.username,
            'fullname': user.fullname,
            'roles': user.roles,
        }
        response.set_cookie(
            key='session',
            value=jwt.encode(payload, 'session', 'HS256'),
            domain=config.SERVER_NAME if config.PROD else None,
            secure=bool(config.PROD),
            samesite='none' if config.PROD else 'lax',
        )
        return response

    def get_unauthenticated_response(self) -> RedirectResponse:
        """Get the redirection response for unauthenticated users."""
        url = self._get_redirection_url()
        url = urljoin(url, '/#/login/?auth_failed=true')
        return RedirectResponse(url, status_code=302)

    @asyncify
    def check_session_token(
        self, session_token: str | None = None
    ) -> Response:
        """Send the request to check the session token.

        Parameters
        ----------
        session_token : str | None, optional
            Session token, by default None.

        Returns
        -------
        Response
            Response of the authentication service.

        Raises
        -------
        BadRequestError
            If could not establish connection to auth server.
        BadRequestError
            If auth server has timed out.
        BadRequestError
            If an unknown error occurred.

        """
        session = TLSSession(timeout=(30.25, 60))
        try:
            url = self._get_auth_server_url()
            response = session.get(
                url, headers={'x-apptag-token': session_token}
            )
            session.close()
            return response

        except ConnectionError:
            session.close()
            raise BadRequestError(
                'No se pudo establecer una conexión con el servidor de autenticación.'
            )

        except Timeout:
            session.close()
            raise BadRequestError(
                'El servidor de autenticación ha tardado mucho en responder.'
            )

        except Exception as e:
            session.close()
            raise BadRequestError(f'Ha ocurrido un error: {str(e)}.')

    async def login(
        self, user: UserSession, is_internal_req: bool
    ) -> HTMLResponse | RedirectResponse:
        """Authenticate a user on SEUS 4.

        Parameters
        ----------
        user : UserSession
            User session.
        is_internal_req : bool
            Whether request is sent from another container
            in the internal network, such as frontend.

        Returns
        -------
        HTMLResponse
            Response containing the SEUS login form.
        RedirectResponse
            Response containing the session token.

        Raises
        ------
        BadRequestError
            If an unknown error occurred.

        """
        if user.is_authenticated:
            return self.get_authenticated_response(user)

        if not is_internal_req:
            return self.get_unauthenticated_response()

        response = await self.check_session_token(user.session_token)
        if response.status_code != UNAUTHORIZED:
            raise BadRequestError(
                'Ha ocurrido un error en el inicio de sesión.'
            )

        return HTMLResponse(response.text, status_code=401)

    async def get_logout_url(self) -> str:
        """Get the logout URL.

        Returns
        -------
        str
            Logout URL.

        """
        return await self.seus.get_logout_url()
