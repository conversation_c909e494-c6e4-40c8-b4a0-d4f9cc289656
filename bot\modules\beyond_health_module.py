from typing import cast
from urllib.parse import urlencode

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import (
    BeyondHealthRemovedUserResponseDto,
    BeyondHealthResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import Automation<PERSON>rror, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import get_form_data, get_html_soup, retry, strip_text

APP_URL = 'https://bhpos.suramericana.com/MainFrame.aspx'
BASE_PLATFORM_URL = 'https://bhpos.suramericana.com/%s'
USER_QUERY_FORM_PATH = 'WebForms/Security/UserAdministration.aspx'
USER_ROLES_FORM_PATH = 'WebForms/Security/AssignRole.aspx'
MENU_PATH = 'Menu.aspx'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded'}


class BeyondHealthModule(BaseModule):
    """Provide functions to consult and remove
    a user on Beyond Health.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_menu_metadata(self) -> FormData:
        """Get the menu metadata required to get the menu content.

        Returns
        -------
        FormData
            Menu metadata.

        Raises
        -------
        AutomationError
            If menu metadata could not be obtained.

        """
        response = self.session.get(BASE_PLATFORM_URL % MENU_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los metadatos del menú.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup,
            'No se encontraron los metadatos del menú.',
        )

    def _get_menu(self, menu_metadata: FormData) -> None:
        """Get the menu of the main page.

        Parameters
        ----------
        menu_metadata : FormData
            Menu metadata.

        Raises
        -------
        AutomationError
            If menu could not be obtained.

        """
        response = self.session.request(
            method=menu_metadata.method,
            url=BASE_PLATFORM_URL % MENU_PATH,
            data=menu_metadata.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú.',
                detail=response.text,
            )

    def _get_main_page(self) -> None:
        """Get the main page containing the menu.

        It is also the last login step.
        """
        menu_component_metadata = self._get_menu_metadata()
        self._get_menu(menu_component_metadata)

    def _get_user_query_form_metadata(self) -> FormData:
        """Get the user query form metadata required
        to get the user query form.

        Returns
        -------
        FormData
            User query form metadata.

        Raises
        ------
        AutomationError
            If user query form metadata could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % USER_QUERY_FORM_PATH)
        if not response.ok:
            raise AutomationError(
                message=(
                    'No se pudo obtener los metadatos del formulario'
                    ' de consulta de usuarios.'
                ),
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup,
            error_message=(
                'El usuario de conexión no tiene los permisos'
                ' suficientes para realizar esta consulta.'
            ),
        )

    def _get_user_query_form(
        self,
        user_query_form_metadata: FormData,
    ) -> FormData:
        """Get the user query form.

        Parameters
        ----------
        user_query_form_metadata : FormData
            User query form metadata.

        Returns
        -------
        FormData
            User query form.

        Raises
        ------
        AutomationError
            If user query form could not be fetched.

        """
        payload = {
            '__EVENTTARGET': 'ctl00$ContentPlaceHolder$ddlSearchOption',
            '__SCROLLPOSITIONX': '0',
            '__SCROLLPOSITIONY': '0',
            'ctl00$ContentPlaceHolder$ddlSearchOption': '1',
        }
        user_query_form_metadata.data.update(payload)
        response = self.session.request(
            method=user_query_form_metadata.method,
            url=BASE_PLATFORM_URL % USER_QUERY_FORM_PATH,
            data=user_query_form_metadata.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta de usuarios.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup,
            'No se encontró el formulario de consulta de usuarios.',
        )

    def _extract_user_roles(self, soup: BeautifulSoup) -> list[str]:
        """Extract the roles of the user.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup with the HTML containing the data of the user.

        Returns
        -------
        list[str]
            Roles of the user.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        user_data_table = soup.find(
            'table', {'id': 'ctl00_ContentPlaceHolder_GridView1'}
        )
        if not user_data_table:
            raise NotFoundError()
        user_data_rows = cast(Tag, user_data_table).find_all('tr')
        if not user_data_rows or len(user_data_rows) < 2:
            raise NotFoundError()
        user_data_row = user_data_rows[1]
        user_data_cells = cast(Tag, user_data_row).find_all('td')
        if not user_data_cells:
            raise NotFoundError()
        roles = user_data_cells[-1]
        return [strip_text(roles.text)]

    def _get_user_data_soup(
        self,
        username: str,
        user_query_form: FormData,
    ) -> BeautifulSoup:
        """Get the user main data and return
        a Soup with the content.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        user_query_form : FormData
            User query form.

        Returns
        -------
        BeautifulSoup
            Soup with the user main data.

        Raises
        ------
        AutomationError
            If user could not be obtained.

        """
        payload = {
            '__SCROLLPOSITIONX': '0',
            '__SCROLLPOSITIONY': '0',
            'ctl00$ContentPlaceHolder$ddlSearchOption': '1',
            'ctl00$ContentPlaceHolder$TextBox1': username,
            'ctl00$ContentPlaceHolder$Button1': 'Buscar',
        }
        user_query_form.data.update(payload)
        user_query_form.data.pop('ctl00$ContentPlaceHolder$Button3', None)
        response = self.session.request(
            method=user_query_form.method,
            url=BASE_PLATFORM_URL % USER_QUERY_FORM_PATH,
            data=user_query_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return get_html_soup(response.text)

    def _extract_active_field_value(
        self,
        soup: BeautifulSoup,
    ) -> bool:
        """Extract the ``active`` field value.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user data.

        Returns
        -------
        bool
            Field value.

        """
        is_active_check_box = soup.find(
            'input', {'id': 'ctl00_ContentPlaceHolder_IsAprovedCheckBox'}
        )
        checked = cast(Tag, is_active_check_box).get('checked')
        if is_active_check_box and checked:
            return True
        return False

    def _extract_locked_field_value(
        self,
        soup: BeautifulSoup,
    ) -> bool:
        """Extract the ``locked`` field value.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user data.

        Returns
        -------
        bool
            Field value.

        """
        is_locked_check_box = soup.find(
            'input', {'id': 'ctl00_ContentPlaceHolder_CbxBlock'}
        )
        checked = cast(Tag, is_locked_check_box).get('checked')
        if is_locked_check_box and checked:
            return True
        return False

    def _get_user_whole_data(
        self,
        username: str,
        user_data_soup: BeautifulSoup,
    ) -> str:
        """Get the user whole data.

        Parameters
        ----------
        username : str
            Username.
        user_data_soup : BeautifulSoup
            Soup containing the user main data.

        Returns
        -------
        str
            User whole data.

        Raises
        ------
        AutomationError
            If user whole data could not be fetched.

        """
        user_data_form = get_form_data(
            user_data_soup,
            'No se encontraron todos los datos del usuario.',
        )
        payload = {
            '__SCROLLPOSITIONX': '0',
            '__SCROLLPOSITIONY': '0',
            '__EVENTTARGET': 'ctl00$ContentPlaceHolder$GridView1$ctl02$LbtSelect',
            'ctl00$ContentPlaceHolder$ddlSearchOption': '1',
            'ctl00$ContentPlaceHolder$TextBox1': username,
        }
        user_data_form.data.update(payload)
        user_data_form.data.pop('ctl00$ContentPlaceHolder$Button1', None)
        user_data_form.data.pop('ctl00$ContentPlaceHolder$Button3', None)
        response = self.session.request(
            method=user_data_form.method,
            url=BASE_PLATFORM_URL % USER_QUERY_FORM_PATH,
            data=user_data_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar los datos completos del usuario.',
                detail=response.text,
            )
        return response.text

    def _get_modify_user_form(self, soup: BeautifulSoup) -> FormData:
        """Get the user modification form.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user data.

        Returns
        -------
        FormData
            User modification form.

        Raises
        ------
        AutomationError
            If form could not be fetched.

        """
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de los datos del usuario.',
        )
        form_data.data.pop('ctl00$ContentPlaceHolder$btnNovelty', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$btnRoles', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$btnPermissions', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$btnParametricTable', None)
        form_data.data.pop(
            'ctl00$ContentPlaceHolder$btnParametricNovelty', None
        )
        form_data.data.pop('ctl00$ContentPlaceHolder$btnPayAgreeCause', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$btnGenerateToken', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$BtnDelete', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$BtnBack', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$IsAprovedCheckBox', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$CbxBlock', None)
        form_data.data.pop('ctl00$ContentPlaceHolder$cbxDomainUser', None)
        response = self.session.request(
            method=form_data.method,
            url=BASE_PLATFORM_URL % USER_QUERY_FORM_PATH,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de modificación del usuario.',
                detail=response.text,
            )
        return get_form_data(
            get_html_soup(response.text),
            'No se encontró el formulario de modificación del usuario.',
        )

    def _disable_user(self, modify_user_form: FormData) -> None:
        """Disable the user.

        Parameters
        ----------
        modify_user_form : FormData
            User modification form.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        modify_user_form.data['ctl00$ContentPlaceHolder$CbxBlockEdit'] = 'on'
        modify_user_form.data.pop(
            'ctl00$ContentPlaceHolder$btnGenerateToken', None
        )
        modify_user_form.data.pop('ctl00$ContentPlaceHolder$BtnCancel', None)
        modify_user_form.data.pop(
            'ctl00$ContentPlaceHolder$IsAprovedEditCheckBox', None
        )
        response = self.session.request(
            method=modify_user_form.method,
            url=BASE_PLATFORM_URL % USER_QUERY_FORM_PATH,
            headers=HEADERS,
            data=urlencode(modify_user_form.data),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo deshabilitar el usuario.',
                detail=response.text,
            )

    def _get_user_roles_form_metadata(self) -> FormData:
        """Get the user roles form metadata required
        to get the users roles form.

        Returns
        -------
        FormData
            User roles form metadata.

        Raises
        ------
        AutomationError
            If user roles form metadata could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % USER_ROLES_FORM_PATH)
        if not response.ok:
            raise AutomationError(
                message=(
                    'No se pudo obtener los metadatos del formulario'
                    ' de roles de usuario.'
                ),
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup,
            error_message=(
                'El usuario de conexión no tiene los permisos suficientes'
                ' para realizar esta consulta.'
            ),
        )

    def _get_user_roles_form(
        self,
        user_roles_metadata_form: FormData,
        username: str,
    ) -> FormData:
        """Get the user roles form.

        Parameters
        ----------
        user_roles_metadata_form : FormData
            User roles metadata form.
        username : str
            Username of the user.

        Returns
        -------
        FormData
            User roles form.

        Raises
        ------
        AutomationError
            If roles form could not be fetched.
        NotFoundError
            If user was not found.

        """
        user_roles_metadata_form.data['ctl00$ContentPlaceHolder$TextBox1'] = (
            username
        )
        user_roles_metadata_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$InsertBtn', None
        )
        user_roles_metadata_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$UpdateBtn', None
        )
        user_roles_metadata_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$DeleteBtn', None
        )
        response = self.session.request(
            method=user_roles_metadata_form.method,
            url=BASE_PLATFORM_URL % USER_ROLES_FORM_PATH,
            data=user_roles_metadata_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de roles del usuario.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        if not soup.find(id='ctl00_ContentPlaceHolder_GridView1'):
            raise NotFoundError()
        return get_form_data(
            soup,
            'No se encontró el formulario de roles del usuario.',
        )

    def _get_user_roles_assignment_form_content(
        self,
        user_roles_form: FormData,
    ) -> str:
        """Get the content of the roles assignment form.

        Parameters
        ----------
        user_roles_form : FormData
            User roles form.

        Returns
        -------
        str
            Content of the roles assignment form.

        Raises
        ------
        AutomationError
            If roles assignment form could not be fetched.

        """
        user_roles_form.data['__EVENTTARGET'] = (
            'ctl00$ContentPlaceHolder$GridView1$ctl02$LbtSelect'
        )
        user_roles_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$InsertBtn', None
        )
        user_roles_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$UpdateBtn', None
        )
        user_roles_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$DeleteBtn', None
        )
        user_roles_form.data.pop('ctl00$ContentPlaceHolder$Button2', None)
        response = self.session.request(
            method=user_roles_form.method,
            url=BASE_PLATFORM_URL % USER_ROLES_FORM_PATH,
            data=user_roles_form.data,
        )
        if not response.ok:
            raise AutomationError(
                message=(
                    'No se pudo obtener el formulario de asignación'
                    ' de roles de usuario.'
                ),
                detail=response.text,
            )
        return response.text

    def _extract_current_roles(
        self,
        user_roles_assignment_form_soup: BeautifulSoup,
    ) -> list[str]:
        """Extract current roles of the user.

        Parameters
        ----------
        user_roles_assignment_form_soup : BeautifulSoup
            Soup of the user roles assignment form.

        Returns
        -------
        list[str]
            List of current roles.

        """
        current_role_list = user_roles_assignment_form_soup.find(
            id='ctl00_ContentPlaceHolder_ListBox2'
        )
        if not current_role_list:
            return []
        roles = cast(Tag, current_role_list).find_all('option')
        return [role.get('value', '') for role in roles]

    def _remove_user_roles(
        self,
        user_roles_assignment_form_content: str,
    ) -> BeyondHealthRemovedUserResponseDto:
        """Remove the roles of the user.

        Parameters
        ----------
        user_roles_assignment_form_content : str
            Content of the roles assignment form.

        Returns
        -------
        BeyondHealthRemovedUserResponseDto
            Removed roles from the user.

        Raises
        ------
        AutomationError
            If roles could not be eliminated.

        """
        soup = get_html_soup(user_roles_assignment_form_content)
        roles = self._extract_current_roles(soup)
        if not roles:
            message = (
                'El usuario ha sido desactivado y bloqueado, sin embargo,'
                ' no se encontraron roles para este usuario.'
            )
            return BeyondHealthRemovedUserResponseDto(
                active=False,
                locked=True,
                warning=True,
                removed_roles=[],
                message=message,
            )

        user_roles_assignment_form = get_form_data(
            soup,
            'No se encontró el formulario de asignación de roles.',
        )
        user_roles_assignment_form.data.pop(
            'ctl00$ContentPlaceHolder$WucCRUDControl1$BackBtn', None
        )
        user_roles_assignment_form.data.pop(
            'ctl00$ContentPlaceHolder$Pasar1', None
        )
        user_roles_assignment_form.data.pop(
            'ctl00$ContentPlaceHolder$Tomar1', None
        )
        response = self.session.request(
            method=user_roles_assignment_form.method,
            url=BASE_PLATFORM_URL % USER_ROLES_FORM_PATH,
            data=user_roles_assignment_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo eliminar los roles del usuario.',
                detail=response.text,
            )
        return BeyondHealthRemovedUserResponseDto(
            active=False,
            locked=True,
            message='Usuario retirado correctamente.',
            removed_roles=roles,
        )

    def _create_user_data_dto(
        self,
        user_info: str,
        roles: list[str],
    ) -> BeyondHealthResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.
        roles: list[str]
            Roles of the user.

        Returns
        -------
        BeyondHealthResponseDto
            Requested user data.

        """
        soup = get_html_soup(user_info)
        return BeyondHealthResponseDto(
            active=self._extract_active_field_value(soup),
            locked=self._extract_locked_field_value(soup),
            roles=roles,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> BeyondHealthResponseDto:
        """Consult the user by its username and get its data.

        Parameters
        ----------
        username : str
            Username to consult.

        Returns
        -------
        BeyondHealthResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            self._get_main_page()
            user_query_form_metadata = self._get_user_query_form_metadata()
            user_query_form = self._get_user_query_form(
                user_query_form_metadata
            )
            user_data_soup = self._get_user_data_soup(
                username, user_query_form
            )
            roles = self._extract_user_roles(user_data_soup)
            user_whole_data = self._get_user_whole_data(
                username, user_data_soup
            )
            return self._create_user_data_dto(user_whole_data, roles)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> BeyondHealthRemovedUserResponseDto:
        """Disable a user and removes its roles.

        Parameters
        ----------
        username : str
            User's username.

        Returns
        -------
        BeyondHealthRemovedUserResponseDto
            Removed roles from the user.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            self._get_main_page()
            user_query_form_metadata = self._get_user_query_form_metadata()
            user_query_form = self._get_user_query_form(
                user_query_form_metadata
            )
            user_data_soup = self._get_user_data_soup(
                username, user_query_form
            )
            user_whole_data = self._get_user_whole_data(
                username, user_data_soup
            )
            soup = get_html_soup(user_whole_data)
            modify_user_form = self._get_modify_user_form(soup)
            self._disable_user(modify_user_form)
            user_roles_metadata_form = self._get_user_roles_form_metadata()
            user_roles_form = self._get_user_roles_form(
                user_roles_metadata_form, username
            )
            user_roles_assignment_form = (
                self._get_user_roles_assignment_form_content(user_roles_form)
            )
            return self._remove_user_roles(user_roles_assignment_form)
        finally:
            self.close_session()
