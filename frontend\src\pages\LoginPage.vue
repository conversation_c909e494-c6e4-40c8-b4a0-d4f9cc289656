<template>
  <q-page>
    <!-- <login-component @on-login="onLogin" /> -->
    <single-sign-on-component />
  </q-page>
</template>

<script setup lang="ts">
//#region imports
import { onMounted } from 'vue';
import { useQuasar } from 'quasar';

// import LoginComponent from 'src/components/auth/LoginComponent.vue';
import SingleSignOnComponent from 'src/components/auth/SingleSignOnComponent.vue';
//#endregion

const $q = useQuasar();

// const onLogin = (token: string): void => {
//   if(UserSession.loadFromToken(token)) {
//     window.location.replace('/#/');
//   } else {
//     window.location.reload();
//   }
// }

const checkLoginMessage = (): void => {
  const message = $q.localStorage.getItem<string>('loginMessage');
  if(!message) return;
  $q.notify({
    message: message,
    color: 'negative',
    position: 'top'
  });
  $q.localStorage.removeItem('loginMessage');
}

onMounted(() => {
  checkLoginMessage();
});
</script>

<style>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.logo {
  width: 150px;
  height: auto;
}
</style>
