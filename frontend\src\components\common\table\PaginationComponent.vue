<template>
  <div class="row justify-between items-end">
    <span class="col">{{ paginationDetails }}</span>
    <q-pagination
      class="col justify-end full-width"
      v-model="page"
      :max="props.pagesNumber"
      color="primary"
      max-pages="7"
      boundary-links
      direction-links />
  </div>
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
//#endregion

//#region types
interface PaginationProps {
  page?: number;
  pagesNumber?: number;
  rowsNumber?: number;
  rowsPerPage?: number;
}

interface PaginationEmits {
  (e: 'onChangePage', page: number): void;
}
//#endregion

const { t } = useI18n();

const props = withDefaults(defineProps<PaginationProps>(), {
  page: 1,
  pagesNumber: 0,
  rowsNumber: 0,
  rowsPerPage: 0
});

const emits = defineEmits<PaginationEmits>();

const page = computed<number>({
  get() {
    return props.page;
  },
  set(page: number) {
    emits('onChangePage', page);
  }
});

const paginationDetails = computed<string>(() => {
  let endNumber = props.page * props.rowsPerPage;
  if(endNumber > props.rowsNumber) endNumber = props.rowsNumber;
  let startNumber = props.rowsNumber ? 1 : 0;
  if(props.page > 1) startNumber = ((props.page - 1) * props.rowsPerPage) + 1;
  return t('showingRowsNumber', {
      rowsNumber: props.rowsNumber,
      startNumber: startNumber,
      endNumber: endNumber
    });
});
</script>
