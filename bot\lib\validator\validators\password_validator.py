import re
import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class PasswordValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, message: str | None = None, length: int = 8) -> None:
        super().__init__(value)
        self.message = message if message else 'invalid_password'
        self.format_values = {'length': length}
        self.length = length

    async def validate(self) -> bool:
        value = self.value()
        validate_fields: str = r"^((?=.*[A-Z])[a-zA-Z!#$%&\.'*+\/=?\´\[^\]\(¨\)°º;:,\\¬ç_ª`{|}~-](?=.+[0-9])|(?=.*[A-Z])[0-9](?=.*[!#$%&\.'*+\/=?\´\[^\]\(¨\)°º;:,\\¬ç_ª`{|}~-]))(?=.*[a-z])([A-Za-z0-9\d!#$%&\.'*+\/¿¡=?^_`{|}~-]|[^ ]){length,50}$".replace('length', str(self.length - 1))
        self.is_valid = re.search(validate_fields, value) is not None if value else True
        return self.is_valid
