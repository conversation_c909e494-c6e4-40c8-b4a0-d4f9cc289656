import json
from typing import cast

from starlette.datastructures import FormD<PERSON>, UploadFile

from i18n import I18<PERSON>
from lib import config
from lib.base_injectable import BaseInjectable
from lib.base_validator import BaseValidator
from lib.data_frame_tools import DataFrame
from lib.validator.data_validator import DataValidator
from lib.validator.error_validator import <PERSON><PERSON>rValidator
from lib.validator.validator import Validator
from services.automation_service import AutomationService


class AutomationValidator(BaseInjectable, BaseValidator):
    """Validate request data of the automation controller endpoints."""

    service: AutomationService

    def __init__(
        self,
        service: AutomationService,
        data_frame: DataFrame,
        i18n: I18N,
    ) -> None:
        self.service = service
        self.data_frame = data_frame
        self.i18n = i18n
        self.usernames = []
        self.media = None

    def get_usernames(self, usernames: str | list[str]) -> list[str]:
        """Get unique usernames.

        Parameters
        ----------
        usernames : str | list[str]
            Usernames.

        Returns
        -------
        list[str]
            List of usernames

        """
        if isinstance(usernames, str):
            usernames = usernames.split(',')
        unique_usernames = []
        for username in usernames:
            username = username.strip().upper()
            if username not in unique_usernames:
                unique_usernames.append(username)
        return unique_usernames

    def validator(self, username: str | None) -> DataValidator:
        """Create a validator for the fields
        of each row of the uploaded file.

        The fields are the usernames to consult.

        Parameters
        ----------
        username : str | None
            Username to consult.

        Returns
        -------
        DataValidator
            Data validator for the passed username.

        """
        validator = DataValidator()
        validator.add('username', Validator[str | None](username).required())
        return validator

    async def validate(self, form: FormData) -> bool:
        """Validate the form data containing the username list or file.

        Parameters
        ----------
        form : FormData
            Form data.

        Returns
        -------
        bool
            Whether the form data is valid or not.

        """
        errors = {}
        try:
            run_by_file = form.get('runByFile')
            usernames = form.get('usernames')
            users_file = form.get('usersFile')

            if run_by_file:
                run_by_file = json.loads(cast(str, run_by_file))

            if usernames:
                self.usernames = self.get_usernames(cast(str, usernames))

            if users_file:
                self.media = cast(UploadFile | None, users_file)

            if run_by_file:
                if not self.media or not isinstance(self.media, UploadFile):
                    errors['common'] = [
                        'No se recibió el archivo con los usuarios a consultar.'
                    ]
            else:
                if not self.usernames or not isinstance(self.usernames, list):
                    errors['common'] = [
                        'No se recibieron los usuarios a consultar.'
                    ]

        except Exception:
            errors['common'] = 'Ha ocurrido un error al obtener los datos.'

        if errors:
            self.error = ErrorValidator()
            self.error.errors = errors  # type: ignore
            return False
        return True

    async def validate_upload(self) -> bool:
        """Validate the uploaded file.

        Parameters
        ----------
        file : UploadFile
            Uploaded file.

        Returns
        -------
        bool
            Whether the file content is valid.

        """
        if not self.media:
            return True

        await self.data_frame.read_from_file(self.media)
        rows = self.data_frame.get_rows(min_row=1, max_col=1, values_only=True)
        if not rows:
            self.error = ErrorValidator()
            self.error.add(
                'common',
                'La primera columna del archivo no contiene ningún usuario.',
            )
            return False
        if len(rows) > config.CONSULT_USERS_MAX:
            self.error = ErrorValidator()
            self.error.add(
                'common',
                f'La cantidad máxima permitida es de'
                f' {config.CONSULT_USERS_MAX} usuarios por archivo.',
            )
            return False

        errors = []
        read_usernames = []

        for index, row in enumerate(rows):
            if self.data_frame.row_is_empty(row):
                continue
            username = self.data_frame.get_row_value(row, 0)
            validator = self.validator(username)
            if await validator.validate() and username:
                read_usernames.append(username)
                continue
            errors.append(
                {f'row-{index+2}': validator.get_error(self.i18n).errors}
            )
        self.usernames = self.get_usernames(read_usernames)

        if errors:
            self.error = ErrorValidator()
            self.error.errors = errors  # type: ignore
            return False
        return True
