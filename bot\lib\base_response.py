import typing
from typing import Any
from starlette import responses
from starlette.background import BackgroundTask


class BaseResponse(responses.JSONResponse):

    def __init__(self, content: Any, status_code: int = 200, headers: typing.Optional[typing.Dict[str, str]] = None, media_type: typing.Optional[str] = None, background: typing.Optional[BackgroundTask] = None) -> None:
        super().__init__(content, status_code, headers, media_type, background)
