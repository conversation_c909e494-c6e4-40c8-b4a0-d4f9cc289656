from beanie import PydanticObjectId

from dtos.log_dto import LogRequestDto, LogResponseDto
from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.filter import Filter, FormatFilter
from lib.pagination import Pagination
from models.log import Log


class LogService(BaseInjectable, BaseService):
    """Service to create, list and export logs."""

    __filter: Filter[Log]

    def __init__(self, _filter: Filter) -> None:
        """Create a dependency object of this service.

        Parameters
        ----------
        _filter : Filter
            Injected dependency of the mongo filters module.

        """
        super().__init__()
        self.__filter = _filter

    async def list_all(
        self,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[LogResponseDto]:
        """Get the list of logs.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the logs,
            by default None.

        Returns
        -------
        Pagination[LogResponseDto]
            Paginated list of logs.

        """
        query = Log.find(
            Log.isDeleted == False,  # noqa: E712
            fetch_links=True,
        )

        if filter_data is not None:
            query = self.__filter.create(filter_data, query, fetch_link=True)

        pagination = Pagination[LogResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = LogResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def get_logs_by_transaction_id(
        self,
        transaction_id: str,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[LogResponseDto]:
        """Retrieve logs by transaction ID.

        Parameters
        ----------
        transaction_id : str
            Transaction ID.

        Returns
        -------
        Pagination[LogResponseDto]
            Paginated list of logs.

        """
        query = Log.find(
            Log.isDeleted == False,  # noqa: E712
            Log.transactionId == PydanticObjectId(transaction_id),
            fetch_links=True,
        )

        if filter_data is not None:
            query = self.__filter.create(filter_data, query, fetch_link=True)

        pagination = Pagination[LogResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = LogResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def create(self, log_dto: LogRequestDto) -> LogResponseDto:
        """Create a log.

        Parameters
        ----------
        log_dto : LogRequestDto
            Log's data

        Returns
        -------
        LogResponseDto
            Created log.

        """
        new_log = Log(**log_dto.to_dict())
        return LogResponseDto.from_orm(await new_log.create())

    async def retrieve(self, id: str) -> LogResponseDto | None:
        """Retrieve a log.

        Parameters
        ----------
        id : str
            Log id.

        Returns
        -------
        LogResponseDto | None
            Log data if found.

        """
        log = await Log.get(PydanticObjectId(id))
        if log is None or log.isDeleted:
            return None
        return LogResponseDto.from_orm(log)
