import asyncio
from datetime import datetime
from time import time
from typing import cast

from lib import config
from lib.injectables import injectable
from lib.logger import logger
from services.automation_service import AutomationService


async def save_consolidated_report() -> None:
    """Save a consolidated report of the deletions
    performed in the current day.
    """
    try:
        hour, minute = config.CONSOLIDATED_TIME.split(':')
        hour = int(hour)
        minute = int(minute)
    except Exception as e:
        logger.error(
            f'Error al procesar el tiempo de consolidados'
            f' "{config.CONSOLIDATED_TIME}": {e}.'
        )
        return None

    if not config.CONSOLIDATED_INTERVAL or config.CONSOLIDATED_INTERVAL > 30:
        logger.error(
            'El intervalo de consolidados es requerido'
            ' y debe ser menor que 30 segundos.'
        )
        return None

    while True:
        now = datetime.now()
        if now.hour == hour and now.minute == minute:
            start_time = time()
            service = cast(
                AutomationService | None, injectable.get(AutomationService)
            )
            if service is None:
                logger.error(
                    f'[Consolidado {now}] No se encontró el servicio'
                    f' de consulta de usuarios retirados.'
                )
                return None
            await service.save_daily_consolidated_results(now)
            end_time = time()
            await asyncio.sleep(
                (3600 * 23) - (end_time - start_time)
            )  # 23 hours

        await asyncio.sleep(config.CONSOLIDATED_INTERVAL)
