import datetime
from typing import Any, Literal

from beanie import PydanticObjectId

from lib.base_dto import BaseRequestDto, BaseResponseDto


class LogRequestDto(BaseRequestDto):
    transactionId: PydanticObjectId
    username: str
    moduleName: str
    moduleFuncArgsSpec: str
    moduleFuncArgs: list[object]
    application: str
    applicationName: str
    data: dict[str, Any] | None = None
    message: str | None = None
    detail: str | None = None
    startTime: float = 0.0
    endTime: float = 0.0
    timeDiff: float = 0.0
    userNotFound: bool = False
    ignored: bool = False
    warning: str | None = None
    status: Literal['done', 'error', 'ignored', 'user_not_found']
    action: Literal['consult', 'remove'] = 'consult'
    consultedBy: str | None = None
    removedBy: str | None = None


class LogResponseDto(BaseResponseDto):
    id: PydanticObjectId
    transactionId: PydanticObjectId
    username: str
    moduleName: str
    moduleFuncArgsSpec: str
    moduleFuncArgs: list[object]
    application: str
    applicationName: str
    data: dict[str, Any] | None = None
    message: str | None = None
    detail: str | None = None
    startTime: float = 0.0
    endTime: float = 0.0
    timeDiff: float = 0.0
    userNotFound: bool = False
    ignored: bool = False
    warning: str | None = None
    status: Literal['done', 'error', 'ignored', 'user_not_found']
    action: Literal['consult', 'remove'] = 'consult'
    consultedBy: str | None = None
    removedBy: str | None = None
    createdAt: datetime.datetime
