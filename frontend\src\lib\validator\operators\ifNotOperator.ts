//#region imports
import ClassOperator from 'src/lib/validator/classOperator';
//#endregion

class IfNotOperator extends ClassOperator {

  public constructor(evalCondition: boolean | (() => boolean)) {
    super(evalCondition);
  }

  public async validate(): Promise<boolean> {
    const evalCondition = this.evalCondition();
    return (evalCondition === true) ? this.isValid = false : this.isValid = true;
  }
}

export default IfNotOperator;