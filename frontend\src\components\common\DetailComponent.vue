<template>
  <div :class="props.class">
    <q-field v-if="props.isTextArea == false" :label="props.label" stack-label borderless readonly>
      <template v-slot:control>
        <div class="self-center full-width no-outline" tabindex="0">
          <q-icon v-if="props.icon && props.iconPosition == 'start'" :name="props.icon" :color="props.colorIcon" />
          {{props.value}}
          <q-icon v-if="props.icon && props.iconPosition == 'end'" :name="props.icon" :color="props.colorIcon" />
        </div>
      </template>
    </q-field>
    <div v-if="typeof textarea == 'string' && props.isTextArea">
      <q-input v-model="textarea" stack-label autogrow borderless readonly type="textarea" :label="props.label"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
//#endregion

//#region types
interface ClassDetailComponent {
  [key: string]: boolean;
}

interface FieldProps {
  class?: ClassDetailComponent | string;
  label: string;
  value: unknown;
  icon?: string;
  isTextArea?: boolean;
  iconPosition?: 'start' | 'end';
  colorIcon?: string;
}
//#endregion

const textarea = computed(() => props.value);

const props = withDefaults(defineProps<FieldProps>(), {
  class: () => { return {'col col-md-6 col-sm-12 col-xs-12': true} },
  iconPosition: 'start',
  isTextArea: false,
})
</script>

