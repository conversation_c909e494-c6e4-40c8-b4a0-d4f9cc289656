from lib import config
from lib.salud_web_session import SaludWebCredentials, SaludWebSession
from lib.saml import SAML


class SaludWebRetirosSession(SaludWebSession):
    """Perform the authentication on Salud Web platform
    for the Bot Retiros user.
    """

    def __init__(self, saml: SAML) -> None:
        super().__init__(saml)

    def get_credentials(self) -> SaludWebCredentials:
        return SaludWebCredentials(
            username=str(config.RETIROS_APPS_CONNECTION_USER),
            password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
        )
