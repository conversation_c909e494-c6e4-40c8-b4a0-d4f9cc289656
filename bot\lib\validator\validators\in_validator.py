import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=object)


class InValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, data: list[object], message: str | None = None) -> None:
        super().__init__(value)
        self.message = message if message else 'invalid_option'
        self.data = data

    async def validate(self) -> bool:
        value = self.value()
        self.is_valid = value in self.data if value else True
        return self.is_valid
