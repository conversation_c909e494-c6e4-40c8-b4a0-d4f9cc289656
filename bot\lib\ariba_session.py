import time
from typing import cast
from urllib.parse import urljoin

from bs4 import BeautifulSoup, Tag
from requests import Response, Session
from requests.cookies import RequestsCookieJar

from lib import config
from lib.base_injectable import BaseInjectable
from lib.exceptions import AutomationError
from lib.http.sessions import TLSSession
from lib.saml import SAML
from lib.tools import get_form_data, get_html_soup

APP_URL = 'https://seus.sura.com/idp/idp-initiated/app/Ariba'


class AribaSession(BaseInjectable):
    """Manage the session on Ari<PERSON>."""

    def __init__(self, saml: SAML) -> None:
        self.saml = saml
        self.__login_response = None
        self.__auth_cookies = RequestsCookieJar()

    def authenticate(self) -> Session:
        """Perform the SAML-IDP authentication."""
        self.close_session()
        self.create_session()

        self.set_cookies()
        if self.authenticated:
            return self.saml.session

        self.clear_cookies()
        self.__login_response = self.login()
        self.save_cookies()

        return self.saml.session

    @property
    def authenticated(self) -> bool:
        """Check if user is authenticated.

        User is authenticated if it has stored the login response.
        """
        if self.__login_response is None:
            return False
        response = self.saml.session.get(self.__login_response.url)
        return response.ok

    def save_cookies(self) -> None:
        """Save the current cookies."""
        self.__auth_cookies.clear()
        self.__auth_cookies.update(self.saml.session.cookies)

    def set_cookies(self) -> None:
        """Set the existing cookies to the current session."""
        self.saml.session.cookies.update(self.__auth_cookies)

    def clear_cookies(self) -> None:
        """Clear the cookies."""
        self.saml.session.cookies.clear()
        self.__auth_cookies.clear()

    def login(self) -> Response:
        """Perform the login in the platform.

        This application requires a SAML-IDP authentication.

        SAML authentication is made by the authenticate() function.

        IDP login is made by sending 4 forms
        (three redirection forms and one timezone form)
        using the following functions:
        - First redirection form
        - Timezone form
        - Second redirection form
        - Third redirection form

        The response of the third redirection form function
        contains the awssk and awr params required by
        the next requests involved in the user consultation.

        Returns
        -------
        Response
            Login response.

        """
        saml_request_form = self.saml.get_saml_request(APP_URL)
        login_form = self.saml.get_login_form(saml_request_form)
        saml_response = self.saml.login(
            login_form,
            str(config.APPS_CONNECTION_USER),
            str(config.APPS_CONNECTION_PASSWORD),
        )
        self.saml.send_saml_response(saml_response)
        saml_login_response = self.saml.get_login_response()
        redirection_response = self.send_first_redirection_form(
            saml_login_response
        )
        redirection_response_soup = get_html_soup(redirection_response.text)
        timezone_response = self.send_timezone_form(
            redirection_response_soup, redirection_response
        )
        second_redirection_response = self.send_second_redirection_form(
            timezone_response
        )
        return self.send_third_redirection_form(second_redirection_response)

    def send_first_redirection_form(self, response: Response) -> Response:
        """Send the first login redirection form.

        NOTE: To understand why is this function necessary
        check the docstring of the login() function.

        Parameters
        ----------
        response : Response
            Response of the SAML authentication.

        Returns
        -------
        Response
            The main page response if user is already logged in.
            Otherwise, the timezone form response.

        Raises
        ------
        AutomationError
            If SAMLResponse was not found.
        AutomationError
            If form could not be sent.

        """
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            error_message='No se encontró el formulario de redirección.',
        )
        saml_response = form_data.data.get('SAMLResponse')
        if not saml_response:
            raise AutomationError(
                message=(
                    'No se encontró el SAMLResponse del formulario'
                    ' de redirección.'
                ),
                detail=str(form_data.data),
            )
        form_data.data['SAMLResponse'] = saml_response.replace('\n', '')
        response = self.saml.session.request(
            method=form_data.method,
            url=urljoin(response.url, form_data.action),
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de redirección.',
                detail=response.text,
            )
        return response

    def send_timezone_form(
        self,
        soup: BeautifulSoup,
        redirection_response: Response,
    ) -> Response:
        """Send the login timezone form.

        NOTE: To understand why is this function necessary
        check the docstring of the login() function.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup containing the response content of
            the redirection form function.
        redirection_response : Response
            Response of the first redirection form function.

        Returns
        -------
        Response
            Last redirection form response.

        Raises
        ------
        AutomationError
            If SAMLResponse was not found.
            If form could not be sent.

        """
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de zona horaria.',
        )
        saml_response = form_data.data.get('SAMLResponse')
        if not saml_response:
            raise AutomationError(
                message=(
                    'No se encontró el SAMLResponse del formulario'
                    ' de zona horaria.'
                ),
                detail=str(form_data.data),
            )
        form_data.data['SAMLResponse'] = saml_response.replace('\n', '')
        form_data.data['timezone'] = '0'
        form_data.data['timezoneFeb'] = '0'
        form_data.data['timezoneMar'] = '0'
        form_data.data['timezoneApr'] = '0'
        form_data.data['timezoneAug'] = '0'
        form_data.data['clientTime'] = str(round(time.time() * 1000))
        form_data.data['clientTimezone'] = 'America/Bogota'
        response = self.saml.session.request(
            method=form_data.method,
            url=urljoin(redirection_response.url, form_data.action),
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de zona horaria.',
                detail=response.text,
            )
        return response

    def send_second_redirection_form(
        self,
        timezone_response: Response,
    ) -> Response:
        """Send the second login redirection form.

        NOTE: To understand why is this function necessary
        check the docstring of the login() function.

        Parameters
        ----------
        timezone_response : str
            Response of the timezone form function.

        Returns
        -------
        Response
            Third redirection form response.

        Raises
        ------
        AutomationError
            If SAMLResponse was not found.
        AutomationError
            If form could not be sent.

        """
        soup = get_html_soup(timezone_response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de la segunda redirección.',
        )
        saml_response = form_data.data.get('SAMLResponse')
        if not saml_response:
            raise AutomationError(
                message=(
                    'No se encontró el SAMLResponse del formulario'
                    ' de la segunda redirección.'
                ),
                detail=str(form_data.data),
            )
        form_data.data['SAMLResponse'] = saml_response.replace('\n', '')
        response = self.saml.session.request(
            method=form_data.method,
            url=urljoin(timezone_response.url, form_data.action),
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de la segunda redirección.',
                detail=response.text,
            )
        return response

    def send_third_redirection_form(
        self,
        second_redirection_response: Response,
    ) -> Response:
        """Send the third login redirection form.

        NOTE: To understand why is this function necessary
        check the docstring of the login() function.

        Parameters
        ----------
        second_redirection_response : Response
            Response of the second redirection form function.

        Returns
        -------
        Response
            Login last response.

        Raises
        ------
        AutomationError
            If SAMLResponse was not found.
        AutomationError
            If form could not be sent.

        """
        soup = get_html_soup(second_redirection_response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de la tercera redirección.',
        )
        saml_response = form_data.data.get('SAMLResponse')
        if not saml_response:
            raise AutomationError(
                message=(
                    'No se encontró el SAMLResponse del formulario'
                    ' de la tercera redirección.'
                ),
                detail=str(form_data.data),
            )
        form_data.data['SAMLResponse'] = saml_response.replace('\n', '')
        response = self.saml.session.request(
            method=form_data.method,
            url=urljoin(second_redirection_response.url, form_data.action),
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de la tercera redirección.',
                detail=response.text,
            )
        return response

    def get_login_response(self) -> Response:
        """Return the response of the SAMLResponse
        service (last login response).

        Raises
        ------
        AutomationError
            If authentication was not successful.

        """
        if not self.__login_response:
            raise AutomationError(
                'La autenticación no ha tenido éxito o no se ha realizado.'
            )
        return self.__login_response

    def create_session(self) -> None:
        """Create a TLSSession instance."""
        self.saml.session = TLSSession()

    def close_session(self) -> None:
        """Close the session."""
        if hasattr(self.saml, 'session'):
            self.saml.session.close()

    def logout(self, ariba_logout_response: str) -> None:
        """Logout.

        Parameters
        ----------
        ariba_logout_response : str
            Logout response.

        """
        self.__login_response = None
        self._sso_logout(ariba_logout_response)
        self.clear_cookies()
        self.close_session()

    def _get_sso_logout_form(self, ariba_logout_response: str) -> str | None:
        """Get the SSO logout form.

        Parameters
        ----------
        ariba_logout_response : str
            Logout response.

        Returns
        -------
        str | None
            SSO logout form.

        """
        soup = get_html_soup(ariba_logout_response)
        logout_iframe = soup.find('iframe')
        if not logout_iframe:
            return None
        sso_logout_url = cast(Tag, logout_iframe).get('src')
        if not sso_logout_url:
            return None
        response = self.saml.session.get(str(sso_logout_url))
        return response.text

    def _sso_logout(self, ariba_logout_response: str) -> None:
        """Logout from SSO.

        Parameters
        ----------
        ariba_logout_response : str
            Logout response.

        """
        logout_form = self._get_sso_logout_form(ariba_logout_response)
        if not logout_form:
            return
        soup = get_html_soup(logout_form)
        logout_form_data = get_form_data(
            soup,
            'No se encontró el formulario de cierre de sesión.',
        )
        self.saml.session.request(
            method=logout_form_data.method,
            url=logout_form_data.action,
        )
