abstract class ClassValidator {
  private _value: unknown;
  public isValid: boolean;
  public message: string;

  public constructor(value: unknown) {
    this._value = value;
    this.isValid = true;
    this.message = '';
  }

  protected value(): unknown {
    let _value: unknown = this._value;
    if(typeof this._value === 'function') _value = this._value();
    return _value;
  }

  abstract validate(): Promise<boolean>;
}

export default ClassValidator;
