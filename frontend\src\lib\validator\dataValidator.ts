//#region imports
import Valida<PERSON> from 'src/lib/validator/validator';
import ErrorValidator from 'src/lib/validator/errorValidator';
//#endregion

//#region types
export interface DataValidatorError {
  [key: string]: string[];
}

interface DataValidatorValidator<T> {
  field: string;
  validator: Validator<T>;
}
//#endregion


class DataValidator {
  isValid: boolean;
  validators: DataValidatorValidator<unknown>[];
  error: ErrorValidator;

  public constructor() {
    this.isValid = true;
    this.validators = [];
    this.error = new ErrorValidator();
  }

  public add<T>(field: string, validator: Validator<T>) {
    this.validators.push({
      field: field,
      validator: validator
    });
  }

  public async validate(): Promise<boolean> {
    this.isValid = true;
    this.error.clear();

    for(const validator of this.validators) {
      if(!await validator.validator.validate()) {
        this.error.add(validator.field, validator.validator.errors);
        this.isValid = false;
      }
    }

    return this.isValid;
  }
}

export default DataValidator;
