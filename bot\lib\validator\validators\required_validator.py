import typing
from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class RequiredValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, message: str | None = None) -> None:
        super().__init__(value)
        self.message = message if message else 'required_field'

    async def validate(self) -> bool:
        value = self.value()
        self.is_valid = (value.strip() != '' if isinstance(value, str) else True) and value is not None and (len(value) != 0 if (isinstance(value, (list, tuple, set))) else True)
        return self.is_valid
