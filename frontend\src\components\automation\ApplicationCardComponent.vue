<template>
  <q-card class="card-container">
    <q-card-section :class="bgStyle">
      <div class="row justify-between items-center no-wrap">
        <div :class="$q.dark.isActive ? 'text-h6 text-bold' : `text-h6 text-bold ${textStyle}`">{{ props.application.applicationName }}</div>
        <div>
          <q-btn v-if="props.onDialog" icon="close" :text-color="iconColor" size="sm" round @click="() => emits('onClose')">
            <q-tooltip class="text-body2" :offset="[10, 10]">
              {{ t('close') }}
            </q-tooltip>
          </q-btn>
          <div v-else-if="props.showSelect">
            <q-checkbox v-model="modelValue" size="md" @update:model-value="(_value: any, _: Event) => emits('onToggle')">
              <q-tooltip class="text-body2" :offset="[10, 10]">
                {{ t('removeUserOnThisApp') }}
              </q-tooltip>
            </q-checkbox>
          </div>
          <q-btn v-else-if="!props.showSelect" icon="crop_free" :text-color="iconColor" size="sm" round @click="() => emits('onExpand', props.application)">
            <q-tooltip class="text-body2" :offset="[10, 10]">
              {{ t('expand') }}
            </q-tooltip>
          </q-btn>
        </div>
      </div>
    </q-card-section>
    <q-card-section style="max-height: 80vh" class="scroll">
      <div v-if="props.onDialog">
        <application-data-component :application="props.application" :removed-user="props.removedUser" />
      </div>
      <q-scroll-area v-else :thumb-style="thumbStyle" :bar-style="barStyle" style="height: 200px; padding-right: 15px">
        <application-data-component :application="props.application" :removed-user="props.removedUser" />
      </q-scroll-area>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
//#region imports
import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';

import type { Application } from 'src/lib/interfaces';

import ApplicationDataComponent from 'src/components/automation/ApplicationDataComponent.vue';
//#endregion

//#region types
interface ApplicationCardProps {
  modelValue?: boolean;
  application: Application;
  onDialog?: boolean;
  showSelect?: boolean;
  removedUser?: boolean;
}

interface ApplicationCardEmits {
  (e: 'update:modelValue', modelValue: boolean): void;
  (e: 'onExpand', application: Application): void;
  (e: 'onToggle'): void;
  (e: 'onClose'): void;
}
//#endregion

const props = withDefaults(defineProps<ApplicationCardProps>(), {
  onDialog: false,
  showSelect: false
});

const emits = defineEmits<ApplicationCardEmits>();

const { t } = useI18n();

const $q = useQuasar();

const thumbStyle = ref<Partial<CSSStyleDeclaration>>({
  right: '2px',
  borderRadius: '5px',
  backgroundColor: '#027be3',
  width: '5px',
});

const barStyle = ref<Partial<CSSStyleDeclaration>>({
  right: '0px',
  borderRadius: '9px',
  backgroundColor: '#027be3',
  width: '9px',
});

const warning = computed<boolean>(() => {
  return props.application.data?.warning === true;
});

const textStyle = computed<string>(() => {
  if(props.application.error) {
    return props.application.userNotFound ? 'text-bold text-orange-7' : 'text-bold text-negative';
  }
  if(props.application.ignored || warning.value) {
    return 'text-bold text-orange-7';
  }
  return 'text-bold text-primary';
});

const bgStyle = computed<string>(() => {
  if($q.dark.isActive) return 'bg-grey-10';
  if(props.application.error) {
    return props.application.userNotFound ? 'bg-yellow-2' : 'bg-red-2';
  }
  if(props.application.ignored || warning.value) {
    return 'bg-yellow-2';
  }
  return 'bg-blue-1';
});

const iconColor = computed<string>(() => {
  if($q.dark.isActive) return 'white';
  if(props.application.error) {
    return props.application.userNotFound ? 'orange-7' : 'negative';
  }
  if(props.application.ignored || warning.value) {
    return 'orange-7';
  }
  return 'primary';
});

const modelValue = computed<boolean>({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emits('update:modelValue', value);
  }
});
</script>

<style scoped>
.card-container {
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  border-radius: 15px;
  box-shadow: 1 10px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.5s;
}

.card-container:hover {
  transform: translateY(-5px);
}
</style>
