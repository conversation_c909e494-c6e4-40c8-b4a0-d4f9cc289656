<template>
  <q-form method="post">
    <information-component v-if="props.showWarningBadge" badge-class="q-pa-xs" text-color="black" information-type="warning" type="badge" :text="t('maxSizeAllowedIs', { size: '25mb' })"/>
    <q-file
      v-model="modelValue"
      :label="label"
      :accept="props.accept"
      :max-file-size="bytesToMegas(25)"
      :loading="loading"
      :multiple="multiple"
      :display-value="filename"
      bottom-slots
      @update:model-value="onUpload"
      @rejected="onRejected">
      <template v-slot:prepend>
        <q-icon name="cloud_upload" @click.stop.prevent />
      </template>
      <template v-slot:append>
        <q-icon name="close" @click.stop.prevent="onClear" class="cursor-pointer" />
      </template>
    </q-file>
    <div class="col col-md-2 q-pt-sm">
      <q-btn
        class="q-mr-xs"
        :label="t('load')"
        color="primary"
        :disable="!modelValue"
        @click="upload"
        v-if="props.uploadButton"/>
      <q-btn
        :label="t('cancel')"
        color="secondary"
        @click="(() => emits('onCancel'))"
        v-if="props.cancelButton"/>
    </div>
  </q-form>
  <modal-component v-model="showErrorModal" type="error" :title="t('error')" :message="errorModalMessage" />
  <modal-component
    v-model="showConfirmModal"
    type="warning"
    confirm-modal
    :title="props.confirmTitle ? props.confirmTitle : t('uploadFile')"
    :message="props.confirmMessage ? props.confirmMessage : t('confirmUpload')"
    @on-continue="confirmModalContinueAction"
    @on-cancel="confirmModalCancelAction" />
</template>

<script setup lang="ts">
//#region imports
import { AxiosError } from 'axios';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import type { QRejectedEntry } from 'quasar';

import { bytesToMegas } from 'src/lib/tools';

import InformationComponent from 'src/components/common/InformationComponent.vue';
import ModalComponent from 'src/components/common/ModalComponent.vue';
//#endregion

//#region types
interface FileProps {
  label?: string;
  accept?: string;
  showWarningBadge?: boolean;
  uploadButton?: boolean;
  cancelButton?: boolean;
  emitErrors?: boolean;
  requestConfirm?: boolean;
  confirmTitle?: string;
  confirmMessage?: string;
  multiple?: boolean;
  uploadService(data: FormData): Promise<unknown>;
}

interface FileEmits {
  (e: 'onResponse', data: unknown): void;
  (e: 'onError', error: AxiosError): void;
  (e: 'onSubmit', files: File | File[]): void;
  (e: 'onCancel'): void;
  (e: 'onClear'): void;
}
//#endregion

const { t } = useI18n();

const emits = defineEmits<FileEmits>();

const props = withDefaults(defineProps<FileProps>(), {
  uploadButton: false,
  cancelButton: false,
  emitErrors: false,
  multiple: false,
  getInBase64: false,
  requestConfirm: false
});

const label = computed<string>(() => props.label ? props.label : `${t('uploadFile')}`);

const loading = ref<boolean>(false);

const showConfirmModal = ref<boolean>(false);

const confirmModalContinueAction = ref<() => unknown>(() => undefined);

const confirmModalCancelAction = ref<() => unknown>(() => undefined);

const showErrorModal = ref<boolean>(false);

const errorModalMessage = ref<string>('');

const modelValue = ref<File | File[] | undefined>(undefined);

const filename = ref<string | undefined>(undefined);

const form = new FormData();

const onClear = (): void => {
  modelValue.value = undefined;
  filename.value = undefined;
  emits('onClear');
}

const onRejected = (rejectedEntries: QRejectedEntry[]): void => {
  for(const entry of rejectedEntries) {
    switch (entry.failedPropValidation) {
      case 'max-file-size':
        errorModalMessage.value = t('maxSizeAllowedExceeded');
        showErrorModal.value = true;
        break;
      case 'accept':
        errorModalMessage.value = t('fileTypeNotAllowed');
        showErrorModal.value = true;
        break;
      default:
        break;
    }
  }
}

const onUpload = async (files: File | File[]): Promise<void> => {
  if(!props.multiple && !Array.isArray(files)) {
    filename.value = files.name;
    form.delete('media');
    form.append('media', files, files.name);
  } else {
    filename.value = (files as File[]).map(f => f.name).join(', ');
  }
  emits('onSubmit', files);
  try {
    if(props.uploadButton == false){
      await upload();
    }
  } catch (error: unknown) {
    emits('onError', (error as AxiosError));
  }
}

const loadMedia = async (): Promise<void> => {
  loading.value = true;
  try {
    const data = await props.uploadService(form);
    emits('onResponse', data);
  } catch (error: unknown) {
    if(props.emitErrors) {
      emits('onError', (error as AxiosError));
    }
    else {
      errorModalMessage.value = t('unableUploadFile');
      showErrorModal.value = true;
    }
  }
  loading.value = false;
}

const upload = async (): Promise<void> => {
  if(props.requestConfirm) {
    confirmModalContinueAction.value = async (): Promise<void> => {
      await loadMedia();
    }
    confirmModalCancelAction.value = () => {
      emits('onCancel');
    }
    showConfirmModal.value = true;
  } else {
    await loadMedia();
  }
  modelValue.value = undefined;
}
</script>
