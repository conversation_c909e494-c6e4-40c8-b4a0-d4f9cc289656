server {
    listen 443 ssl;
    http2 on;
    server_name  ${SERVER_NAME};
    server_tokens   off;

    ssl_certificate /etc/nginx/cert/cert.pem;
    ssl_certificate_key /etc/nginx/cert/key.pem;

    client_max_body_size 5M;

    add_header Access-Control-Allow-Headers "*";
    add_header Access-Control-Allow-Origin *;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Permitted-Cross-Domain-Policies master-only;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Expect-CT "enforce, max-age=300";
    add_header X-Content-Type-Options "nosniff";

    error_page 497 301 =307 https://$host:$server_port$request_uri;

    keepalive_timeout 10s;
    client_header_timeout 10s;
    client_body_timeout 10s;
    send_timeout 10s;

    gzip on;
    gzip_types text/plain text/css application/javascript application/json image/svg+xml;
    gzip_min_length 1024;
    gzip_vary on;

    root /usr/share/nginx/html;
    index index.html;
    charset utf-8;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    access_log /dev/stdout;
    error_log /dev/stderr;

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
