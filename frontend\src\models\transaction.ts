//#region imports
import { i18n } from 'src/boot/i18n';
import type { SelectOption } from 'src/lib/interfaces';
import { type ConsultedUserResponse } from 'src/models/consultedUser';
//#endregion

//#region types
export enum Action {
  consult = 'consult',
  remove = 'remove',
}

export enum State {
  progress = 'progress',
  done = 'done',
  error = 'error',
}

export interface TransactionProgress {
  state: State;
  message: string;
  progress: number;
}

export interface Transaction {
  id: string;
  usernames: string[];
  action: Action;
  state: State;
  message: string;
  progress: number;
  consultedUsers: ConsultedUserResponse[];
  removedUsers: ConsultedUserResponse[];
  updatedAt: Date;
  createdAt: Date;
  createdBy: string;
}

export interface TransactionResponse {
  id: string;
  usernames: string[];
  action: Action;
  state: State;
  message: string;
  progress: number;
  consultedUsers: ConsultedUserResponse[];
  removedUsers: ConsultedUserResponse[];
  updatedAt: string;
  createdAt: string;
  createdBy: string;
}
//#endregion

export class TransactionTools {
  public static load(transactionResponse: TransactionResponse): Transaction {
    return {
      id: transactionResponse.id,
      usernames: transactionResponse.usernames,
      action: transactionResponse.action,
      state: transactionResponse.state,
      message: transactionResponse.message,
      progress: transactionResponse.progress,
      consultedUsers: transactionResponse.consultedUsers,
      removedUsers: transactionResponse.removedUsers,
      createdAt: new Date(transactionResponse.createdAt),
      updatedAt: new Date(transactionResponse.updatedAt),
      createdBy: transactionResponse.createdBy,
    }
  }

  public static loadMany(transactionResponse: TransactionResponse[]): Transaction[] {
    return transactionResponse.map((transactionResponse: TransactionResponse): Transaction => TransactionTools.load(transactionResponse));
  }

  public static getEmptyTransaction(): Transaction {
    return {
      id: '',
      usernames: [],
      action: Action.consult,
      state: State.progress,
      message: '',
      progress: 0.0,
      consultedUsers: [],
      removedUsers: [],
      updatedAt: new Date(),
      createdAt: new Date(),
      createdBy: '',
    }
  }

  public static getState(state: State): string {
    switch (state) {
      case State.progress:
        return i18n.global.t('progress');
      case State.done:
        return i18n.global.t('done');
      case State.error:
        return i18n.global.t('error');
      default:
        return 'N/A';
    }
  }

  public static getStatusOptions(): SelectOption[] {
    return [
      {label: i18n.global.t('progress'), value: State.progress},
      {label: i18n.global.t('updated'), value: State.done},
      {label: i18n.global.t('error'), value: State.error},
    ]
  }

  public static cleanTransaction(transaction: Transaction): void {
    transaction.id = '';
    transaction.message = '';
    transaction.progress = 0;
    transaction.usernames.splice(0);
    transaction.consultedUsers.splice(0);
    transaction.removedUsers.splice(0);
  }
}
