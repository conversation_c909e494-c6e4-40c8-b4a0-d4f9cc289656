from urllib.request import Request
from http.cookiejar import <PERSON><PERSON>, DefaultCookiePolicy


class AllowsEmptyDomainsPolicy(DefaultCookiePolicy):
    """Modifies the `set_ok_domain` function to
    override the domain of a cookies with the
    request host only if the domain is specified
    and the its value is an empty string::

        COOKIE_NAME=cookie_value; domain=; ...

    A `domain=;` cookie attribute would be ignored
    by the `DefaultCookiePolicy` policy as explained
    on:
    - https://github.com/psf/requests/issues/6245#issuecomment-1509824523
    - https://docs.python.org/es/3/library/http.cookiejar.html#http.cookiejar.DefaultCookiePolicy

    NOTE: Use only if it is not posible to assign
    a correct domain to the cookie.
    """

    def set_ok_domain(self, cookie: Cookie, request: Request) -> bool:
        """Checks the domain of the cookie.

        Parameters
        ----------
        cookie : Cookie
            <PERSON>.
        request : Request
            Request.

        Returns
        -------
        bool
            Whether cookie domain is valid.
        """

        if cookie.domain_specified and cookie.domain == '.':
            cookie.domain = request.host
        return super().set_ok_domain(cookie, request)
