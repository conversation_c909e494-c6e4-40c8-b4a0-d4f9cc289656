const fs = require('fs');
const https = require('https');
const express = require('express');
const session = require('express-session');
const fileupload = require('express-fileupload');
const filter = require('sso-sura-node/src/filter/Filter');

const PORT = 3000;

const app = express();

app.get('/health', (_, res) => {
    res.send({ status: true });
});

app.use(fileupload());

app.use(session({
    secret: '19e3a90a-57ea-40a1-b12c-0c3fe0e7d550',
    resave: true,
    saveUninitialized: true,
}));

app.use(filter);

app.get('/', (_, res) => {
    res.send({ authenticated: true });
});

if (process.env.PROD === 'true') {
    const options = {
        key: fs.readFileSync('/etc/ssl/certs/key.pem'),
        cert: fs.readFileSync('/etc/ssl/certs/cert.pem')
    };
    https.createServer(options, app).listen(PORT, () => {
        console.log(`Server listening on https://localhost:${PORT}`);
    });
} else {
    app.listen(PORT, () => {
        console.log(`Server listening on http://localhost:${PORT}`);
    });
}
