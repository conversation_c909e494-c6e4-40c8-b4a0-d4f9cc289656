//#region imports
import EmailValidator from 'src/lib/validator/validators/emailValidator';
import RequiredValidator from 'src/lib/validator/validators/requiredValidator';
import IdCardValidator from 'src/lib/validator/validators/idCardValidator';
import PhoneValidator from 'src/lib/validator/validators/phoneValidator';
import CellPhoneValidator from 'src/lib/validator/validators/cellPhoneValidator';
import GreaterThanValidator from 'src/lib/validator/validators/greaterThanValidator';
import LowerThanValidator from 'src/lib/validator/validators/lowerThanValidator';
import GreaterEqualsThanValidator from 'src/lib/validator/validators/greaterEqualsThanValidator';
import LowerEqualsThanValidator from 'src/lib/validator/validators/lowerEqualsThanValidator';
import RangeValidator from 'src/lib/validator/validators/rangeValidator';
import CustomValidator from 'src/lib/validator/validators/customValidator';
import RegularExpressionValidator from 'src/lib/validator/validators/regularExpressionValidator';
import MaxLengthValidator from 'src/lib/validator/validators/maxLengthValidator';
import ObjectValidator from 'src/lib/validator/validators/objectCompareValidator';
import InValidator from 'src/lib/validator/validators/inValidator';
import RangeLengthValidator from 'src/lib/validator/validators/rangeLengthValidator';
import MinLengthValidator from 'src/lib/validator/validators/minLengthValidator';
import UrlValidator from 'src/lib/validator/validators/urlValidator';
//#endregion

export {
  UrlValidator,
  RangeLengthValidator,
  MinLengthValidator,
  InValidator,
  ObjectValidator,
  MaxLengthValidator,
  RegularExpressionValidator,
  CustomValidator,
  CellPhoneValidator,
  RangeValidator,
  LowerThanValidator,
  GreaterEqualsThanValidator,
  LowerEqualsThanValidator,
  EmailValidator,
  RequiredValidator,
  IdCardValidator,
  PhoneValidator,
  GreaterThanValidator
};
