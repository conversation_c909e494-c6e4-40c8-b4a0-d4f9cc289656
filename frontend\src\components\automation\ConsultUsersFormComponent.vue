<template>
  <q-dialog v-model="modelValue" persistent @before-show="resetForm">
    <q-card style="width: 100%; max-width: 55vw;">
      <q-card-section :class="{ 'bg-primary text-white': !$q.dark.isActive }">
        <div class="row justify-between">
          <div class="text-h6 text-bold">{{ t('consultUsers') }}</div>
          <q-icon name="cancel" class="cursor-pointer" size="sm" @click="onCancel" />
        </div>
      </q-card-section>
      <q-card-section>
        <!-- Execution mode selector -->
        <div
          :class="{ 'text-subtitle1 text-center text-bold': true, 'text-primary': !$q.dark.isActive }">
          {{ t('selectHowToConsultUsers') }}
        </div>
        <div class="row justify-between items-center q-pa-md">
          <span :class="{ 'col text-body1 text-left': true, 'text-bold': !form.data.runByFile }">
            {{ t('inputUsersToConsult') }}
          </span>
          <span class="col-shrink"><q-toggle v-model="form.data.runByFile" color="primary" /></span>
          <span :class="{ 'col text-body1 text-right': true, 'text-bold': form.data.runByFile }">
            {{ t('loadFileWithUsersToConsult') }}
          </span>
        </div>

        <q-form class="q-px-md q-pt-xs" @submit="onSubmit">
          <error-list-component :class="{ 'q-mb-md': form.errors?.common.length }"
            :errors="form.errors?.common" />

          <!-- To type the comma-separated users -->
          <field-component v-if="!form.data.runByFile" v-slot="slotProps"
            :errors="form.errors?.usernames">
            <information-component badge-class="q-pa-xs" text-color="black" information-type="info"
              type="badge" :text="t('separateUsersByComma')" />
            <q-input v-model="form.data.usernames" :error="slotProps.hasErrors"
              :label="t('usersToConsult')" filled />
          </field-component>

          <!-- To upload an Excel/CSV file with the users -->
          <field-component v-else v-slot="slotProps" :errors="form.errors?.usersFile">
            <information-component badge-class="q-pa-xs" text-color="black" information-type="info"
              type="badge" :text="t('uploadUsersToConsultFileInfo')" />
            <q-file v-model="form.data.usersFile" :error="slotProps.hasErrors"
              :label="t('uploadUsersToConsultFile')" filled accept=".xlsx, .xls, .csv"
              :display-value="filename" :max-file-size="bytesToMegas(5)" @rejected="onRejected">
              <template v-slot:prepend>
                <q-icon name="cloud_upload" @click.stop.prevent />
              </template>
              <template v-slot:append>
                <q-icon name="close" @click.stop.prevent="onClearFile" class="cursor-pointer" />
              </template>
            </q-file>
          </field-component>

          <div class="text-center q-mt-md">
            <q-btn class="q-mr-xs" :label="t('consult')" color="primary" type="submit"
              :disable="!form.data.usernames && !form.data.usersFile" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
//#region imports
import { QRejectedEntry, useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import type { AutomationFormData } from 'src/forms/automation/automationForm';
import automationForm from 'src/forms/automation/automationForm';
import { bytesToMegas } from 'src/lib/tools';

import ErrorListComponent from 'src/components/common/ErrorListComponent.vue';
import FieldComponent from 'src/components/common/FieldComponent.vue';
import InformationComponent from 'src/components/common/InformationComponent.vue';
//#endregion

//#region types
interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', showModal: boolean): void;
  (e: 'submit', form: AutomationFormData): void;
}
//#endregion

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const $q = useQuasar();

const { t } = useI18n();

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit('update:modelValue', value);
  }
});

const form = automationForm();

const filename = ref<string>();

function resetForm(): void {
  form.data.runByFile = false;
  form.data.usernames = undefined;
  form.data.usersFile = undefined;
}

async function onSubmit(): Promise<void> {
  if (!await form.validate()) return;
  emit('submit', form.data);
}

function onCancel(): void {
  modelValue.value = false;
  resetForm();
}

function onClearFile(): void {
  form.data.usersFile = undefined;
  filename.value = undefined;
}

function onRejected(rejectedEntries: QRejectedEntry[]): void {
  for (const entry of rejectedEntries) {
    switch (entry.failedPropValidation) {
      case 'max-file-size':
        form.errors?.usersFile.push(t('maxSizeAllowedExceeded'));
        break;
      case 'accept':
        form.errors?.usersFile.push(t('fileTypeNotAllowed'));
        break;
      default:
        break;
    }
  }
}
</script>
