import re
import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class RegularExpressionValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, reg_expression: str, message: str | None = None):
        super().__init__(value)
        self.__reg_expression = reg_expression
        self.message = message if message else 'invalid_value'

    async def validate(self) -> bool:
        value = self.value()
        validate_fields: str = self.__reg_expression
        self.is_valid = re.search(validate_fields, value) is not None if value else True
        return self.is_valid
