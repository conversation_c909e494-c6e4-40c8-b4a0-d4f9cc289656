<template>
  <modal-component v-model="modelValue" :title="t('removeUsers')"
    :message="t('youAreAboutToRemoveUsers')" type="warning" confirm-modal width="700px"
    max-width="80vw" :disable-continue-action="!confirmation"
    :continue-action-label="t('startRemovingUsers')" continue-action-color="negative"
    cancel-action-color="positive" @on-continue="onSubmit" @on-cancel="onCancel">
    <template #body>
      <div class="q-px-md">
        <information-component badge-class="q-pa-xs" text-color="black" information-type="info"
          type="badge" :text="t('catalogNumberInputInfo')" />
        <q-input v-model="catalogNumber" :label="t('catalogNumber')" dense filled />
        <q-separator spaced />
        <div class="text-subtitle2 text-negative text-center">
          {{ t('areYouSureYouWantToRemoveUsers') }}
        </div>
        <div class="q-my-sm">
          <q-table flat dense :rows="usersToBeRemovedTableData"
            :columns="usersToBeRemovedTableColumns" :pagination="{ rowsPerPage: 3 }"
            :rows-per-page-label="t('rowsPerPage')" :rows-per-page-options="[1, 2, 3]"
            :pagination-label="getPaginationLabel" :no-data-label="t('noDataLabel')">
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td :props="props" v-for="col in props.cols" :key="col.name">
                  <template v-if="col.name === 'catalogNumber'">
                    <q-input v-model="catalogNumberMap[props.row['user']]" class="q-pa-none"
                      input-class="text-center" type="text" dense filled
                      :placeholder="catalogNumber" />
                  </template>
                  <template v-else>
                    <div
                      :class="{ 'ellipsis text-no-wrap': col.useEllipsis, 'cursor-pointer': col.useCursorPointer }"
                      :style="{ maxWidth: col.maxWidth ? `${col.maxWidth}px` : undefined }">
                      {{ col.value }}
                    </div>
                    <q-popup-edit v-if="col.usePopUp" class="bg-blue-grey-2" v-model="col.value"
                      :offset="[10, 10]" v-slot="scope" max-width="600px" max-height="400px">
                      <div v-html="scope.value"></div>
                    </q-popup-edit>
                  </template>
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </div>
        <q-checkbox v-model="confirmation" color="negative" :label="t('removeUsersConfirmation')" />
      </div>
    </template>
  </modal-component>
</template>

<script setup lang="ts">
//#region imports
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import type {
  CatalogNumberMap,
  RemoveUsersData,
  RemoveUsersTableData,
  UsersToBeRemovedMap,
} from 'src/lib/interfaces';
import { cleanObject } from 'src/lib/tools';

import InformationComponent from 'src/components/common/InformationComponent.vue';
import ModalComponent from 'src/components/common/ModalComponent.vue';
//#endregion

//#region types
interface Props {
  modelValue: boolean;
  usersToBeRemoved: UsersToBeRemovedMap;
  usersToBeRemovedTableData: RemoveUsersTableData[];
}

interface Emits {
  (e: 'update:modelValue', showModal: boolean): void;
  (e: 'submit', data: RemoveUsersData): void;
  (e: 'cancel'): void;
}

type TableColumns = {
  name: string;
  label: string;
  field: string | ((row: RemoveUsersTableData) => string | undefined);
  required?: boolean;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  usePopUp?: boolean;
  useEllipsis?: boolean;
  useCursorPointer?: boolean;
  maxWidth?: number;
}[];
//#endregion

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const { t } = useI18n();

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit('update:modelValue', value);
  }
});

const catalogNumber = ref<string>();

const catalogNumberMap = reactive<CatalogNumberMap>({});

const confirmation = ref(false);

const usersToBeRemovedTableColumns: TableColumns = [
  {
    name: 'user',
    label: t('username'),
    field: (row: RemoveUsersTableData) => row.user,
    align: 'center',
    sortable: true
  },
  {
    name: 'applications',
    label: t('applications'),
    field: (row: RemoveUsersTableData) => row.applications,
    align: 'center',
    usePopUp: true,
    useEllipsis: true,
    useCursorPointer: true,
    maxWidth: 350
  },
  {
    name: 'catalogNumber',
    label: t('catalogNumber'),
    field: (row: RemoveUsersTableData) => row.catalogNumber,
    align: 'center'
  }
];

function getPaginationLabel(
  firstRowIndex: number,
  endRowIndex: number,
  totalRowsNumber: number,
): string {
  return t('paginationLabel', { firstRowIndex, endRowIndex, totalRowsNumber })
}

function getUserCatalogNumber(username: string): string {
  if (catalogNumberMap.hasOwnProperty(username)) {
    return catalogNumberMap[username] || catalogNumber.value || 'N/A';
  }
  return 'N/A';
}

function resetForm(): void {
  catalogNumber.value = '';
  confirmation.value = false;
  cleanObject(catalogNumberMap);
}

function onSubmit(): void {
  const data: RemoveUsersData = { users: {} };
  Object.entries(props.usersToBeRemoved).forEach(([username, apps]) => {
    data.users[username] = { applications: [], catalogNumber: '' };
    Object.entries(apps).forEach(([app, fields]) => {
      if (fields.removeFrom === true) {
        data.users[username].applications.push(app);
        data.users[username].catalogNumber = getUserCatalogNumber(username);
      }
    });
  });
  emit('submit', data);
  resetForm();
}

function onCancel(): void {
  Object.keys(catalogNumberMap).forEach(username => delete catalogNumberMap[username]);
  modelValue.value = false;
  emit('cancel');
  resetForm();
}
</script>
