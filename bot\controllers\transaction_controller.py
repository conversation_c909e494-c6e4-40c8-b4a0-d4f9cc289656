import json

from sse_starlette import EventSourceResponse

from dtos.transaction_dto import TransactionResponseDto
from lib import config
from lib.auth import AuthRole, auth
from lib.base_controller import BaseController, get
from lib.responses import (
    DtoResponse,
    EmptyResponse,
    ErrorResponse,
    MediaResponse,
    PaginationResponse,
)
from lib.validator.error_validator import ErrorValidator
from services.transaction_service import TransactionService
from validators.transaction_validator import TransactionValidator


class TransactionController(BaseController):
    """Transaction endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/transactions/export/report')
    async def export_report(
        self, validator: TransactionValidator
    ) -> MediaResponse | ErrorResponse:
        """Export a daily or consolidated report.

        Parameters
        ----------
        validator : TransactionValidator
            Injected dependency of the transactions validator.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.

        """
        action = self.request.query_params.get('action', 'consult')
        has_privileges = self.request.query_params.get('hasPrivileges', 'false')
        include_not_found = self.request.query_params.get(
            'includeNotFound', 'false'
        )
        include_errors = self.request.query_params.get(
            'includeErrors', 'false'
        )

        if not await validator.validate_export(
            action, has_privileges, include_not_found, include_errors
        ):
            return ErrorResponse(validator.error)

        if action == 'previous_deletions':
            file = await validator.service.export_last_consolidated(
                include_errors == 'true'
            )
        else:
            file = await validator.service.export_daily_results(
                action=action,  # type: ignore
                has_privileges=has_privileges == 'true',
                include_not_found=include_not_found == 'true',
                include_errors=include_errors == 'true',
            )

        if not file:
            error = ErrorValidator()
            error.add(
                'common',
                'No se encontró ningún reporte de retiros.'
                if action == 'previous_deletions'
                else 'No se encontraron transacciones.',
            )
            return ErrorResponse(error, 404)

        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )

    @auth(AuthRole.CONSULT)
    @get('/transactions')
    async def list(
        self, service: TransactionService
    ) -> PaginationResponse[TransactionResponseDto] | ErrorResponse:
        """Return a paginated list of Transactions.

        Parameters
        ----------
        service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        PaginationResponse[TransactionResponseDto]
            Paginated list of Transactions.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get('limit', config.ROWS_PER_PAGE)
        pagination = await service.list(int(page), int(limit), filter_data)
        return PaginationResponse[TransactionResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/transactions/{id:str}')
    async def retrieve(
        self, id: str, service: TransactionService
    ) -> DtoResponse[TransactionResponseDto] | ErrorResponse:
        """Find a transaction by id.

        Parameters
        ----------
        id : str
            Transaction id.
        service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Generic error.

        """
        transaction = await service.retrieve(id)
        if not transaction:
            error = ErrorValidator()
            error.add('common', 'Transacción no encontrada.')
            return ErrorResponse(error, 404)
        return DtoResponse[TransactionResponseDto](transaction)

    @auth(AuthRole.CONSULT)
    @get('/transactions/progress/by_creator')
    async def find_in_progress_by_creator(
        self, service: TransactionService
    ) -> DtoResponse[TransactionResponseDto] | EmptyResponse | ErrorResponse:
        """Find a transaction in progress by its creator.

        Parameters
        ----------
        service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Generic error.

        """
        transaction = await service.find_in_progress_by_creator(
            self.request.user.username
        )
        if not transaction:
            return EmptyResponse()
        return DtoResponse[TransactionResponseDto](transaction)

    @auth(AuthRole.CONSULT)
    @get('/transactions/{id:str}/progress')
    async def progress(
        self, id: str, service: TransactionService
    ) -> EventSourceResponse | ErrorResponse | EmptyResponse:
        """Find a transaction by id and streams its progress.

        Parameters
        ----------
        id : str
            Transaction id.
        service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        EventSourceResponse
            Transaction progress.
        ErrorResponse
            Generic error.

        """
        transaction_progress = service.get_progress(id)
        return EventSourceResponse(
            transaction_progress,
            headers={'Cache-Control': 'no-cache'},
        )

    @auth(AuthRole.CONSULT)
    @get('/transactions/{id:str}/export')
    async def export(
        self, id: str, service: TransactionService
    ) -> MediaResponse | ErrorResponse:
        """Find a transaction by id and
        exports its consulted users.

        Parameters
        ----------
        id : str
            Transaction id.
        service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.

        """
        file = await service.export_results(id)
        if not file:
            error = ErrorValidator()
            error.add('common', 'La transacción no ha sido encontrada.')
            return ErrorResponse(error, 404)
        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )
