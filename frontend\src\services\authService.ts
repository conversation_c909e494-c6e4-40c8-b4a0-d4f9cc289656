/* eslint-disable @typescript-eslint/no-explicit-any */
//#region imports
import { api } from 'src/boot/axios';
import type { AxiosResponse } from 'axios';
import type { LoginFormData } from 'src/forms/auth/loginForm';
import type { URLResponse } from 'src/lib/interfaces';
//#endregion

export const login = async (data: LoginFormData): Promise<string> => {
  const response: AxiosResponse<void> = await api.post('login', {username: data.username, password: data.password});
  const token = response.headers.authorization;
  return token ? token.replace('Bearer ', '') : '';
}

export const logout = async (): Promise<void> => {
  const response: AxiosResponse<URLResponse> = await api.get('logout');
  window.location.href = response.data.url;
}

export const loginSSO = (): void => {
  const form = document.createElement('form');
  form.action = process.env.API_SERVER + '/login';
  form.method = 'GET';
  document.body.appendChild(form);
  form.submit();
}
