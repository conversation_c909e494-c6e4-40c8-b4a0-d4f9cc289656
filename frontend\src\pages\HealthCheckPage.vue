<template>
  <div v-if="loading" class="flex flex-center window-height window-width">
    <spinner-component />
  </div>
  <div v-else class="fullscreen text-primary text-center q-pa-md flex flex-center">
    <div>
      <div style="font-size: 20vh">
        <q-icon
          :name="serverHealth.status ? 'check_circle' : 'construction'"
          :color="serverHealth.status ? 'positive' : 'negative'" />
      </div>
      <template v-if="serverHealth.status">
        <div class="text-positive text-h6">
          {{t('serverIsAvailable')}}
        </div>
        <q-btn class="q-mt-xl" color="primary" :label="t('goBack')" no-caps @click="goBack" />
      </template>
      <template v-else>
        <div class="text-negative text-h6">
          {{t('serverIsNotAvailable')}}
        </div>
        <div v-if="serverHealth.message" class="message text-subtitle1 text-justify bg-white rounded-borders text-dark q-pa-md q-mx-md">
          <span class="text-weight-bold">{{t('details')}}:</span> {{serverHealth.message}}
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { onMounted, onBeforeUnmount, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { ServerHealth } from 'src/lib/interfaces';
import UserSession from 'src/lib/userSession';

import SpinnerComponent from 'src/components/common/SpinnerComponent.vue';
//#endregion

const loading = ref<boolean>(true);

const { t } = useI18n();

const serverHealth = reactive<ServerHealth>({
  status: false,
  authenticated: false,
  message: t(''),
  enableBotRetiros: false
});

let interval: NodeJS.Timeout;

const goBack = (): void => {
  window.location.href = serverHealth.authenticated ? '/#/' : '/#/login';
}

const checkServerHealth = async (): Promise<void> => {
  try {
    const currentServerHealth = await UserSession.getServerHealth();
    Object.assign(serverHealth, currentServerHealth);
    if(serverHealth.status) {
      clearInterval(interval);
    }
  } catch (error) {
    console.error(error);
  }
  loading.value = false;
}

onMounted(async (): Promise<void> => {
  clearInterval(interval);
  await checkServerHealth();
  if(!serverHealth.status) {
    interval = setInterval(async () => {
      await checkServerHealth();
    }, 5000);
  }
});

onBeforeUnmount((): void => {
  clearInterval(interval);
});
</script>

<style scoped>
.message {
  max-width: 600px;
}
</style>
