from dataclasses import dataclass
from urllib.parse import urljoin

from requests import Response, Session
from requests.cookies import Requests<PERSON>ookie<PERSON>ar

from lib import config
from lib.base_injectable import BaseInjectable
from lib.exceptions import AutomationError
from lib.http.sessions import TLSSession
from lib.saml import SAML
from lib.tools import get_form_data, get_html_soup

APP_URL = 'https://saludweb.suramericana.com/saludweb'


@dataclass
class SaludWebCredentials:
    username: str
    password: str


class SaludWebSession(BaseInjectable):
    """Perform the authentication on Salud Web platform."""

    def __init__(self, saml: SAML) -> None:
        self.saml = saml
        self.__login_response = None
        self.__auth_cookies = RequestsCookieJar()

    def get_credentials(self) -> SaludWebCredentials:
        """Get the credentials of the connection user.

        Returns
        -------
        SaludWebCredentials
            Credentials of the connection user.

        """
        return SaludWebCredentials(
            username=str(config.APPS_CONNECTION_USER),
            password=str(config.APPS_CONNECTION_PASSWORD),
        )

    def authenticate(self) -> Session:
        """Perform the SAML authentication."""
        self.close_session()  # Close session if exists
        self.create_session()

        self.set_cookies()
        if self.authenticated and self.check_session_alive():
            return self.saml.session

        self.clear_cookies()
        self.__login_response = self.login()
        self.save_cookies()

        return self.saml.session

    @property
    def authenticated(self) -> bool:
        """Check if user is authenticated.

        User is authenticated if it has stored the login response.
        """
        return self.__login_response is not None

    def save_cookies(self) -> None:
        """Save the current cookies."""
        self.__auth_cookies.clear()
        self.__auth_cookies.update(self.saml.session.cookies)

    def set_cookies(self) -> None:
        """Set the existing cookies to the current session."""
        self.saml.session.cookies.update(self.__auth_cookies)

    def clear_cookies(self) -> None:
        """Clear the cookies."""
        self.saml.session.cookies.clear()
        self.__auth_cookies.clear()

    def check_session_alive(self) -> bool:
        """Check if the session is still alive.

        Returns
        -------
        bool
            Whether session is alive.

        """
        if self.__login_response is None:
            return False

        response = self.saml.session.get(self.__login_response.url)
        return response.ok

    def login(self) -> Response:
        """Perform the SAML authentication.

        Returns
        -------
        Response
            Login response.

        Raises
        ------
        AutomationError
            If login failed.

        """
        try:
            credentials = self.get_credentials()
            self.saml.authenticate(
                app_url=APP_URL,
                username=credentials.username,
                password=credentials.password,
            )
            return self.saml.fetch_app_page()
        except AutomationError as e:
            if e.detail and 'Cerrar sesión activa' in e.detail:
                soup = get_html_soup(e.detail)
                form_data = get_form_data(soup)
                self.saml.session.request(
                    method=form_data.method,
                    url=urljoin('https://seus.sura.com/', form_data.action),
                    data=form_data.data,
                )
            raise e

    def create_session(self) -> None:
        """Create a TLSSession instance."""
        self.saml.session = TLSSession()

    def close_session(self) -> None:
        """Close the session."""
        if hasattr(self.saml, 'session'):
            self.saml.session.close()

    def logout(self) -> None:
        self.__login_response = None
        self.clear_cookies()
        self.close_session()
