from asyncify import asyncify
from beanie import PydanticObjectId
from datetime import datetime

from lib.export import ExportData, File
from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.filter import Filter, FormatFilter
from lib.pagination import Pagination
from lib.active_directory import ADUser
from lib.results_exporter import ResultsExporter

from dtos.removed_user_dto import RemovedUserResponseDto
from models.removed_user import RemovedUser


class RemovedUserService(BaseInjectable, BaseService):
    """Service to get the current stored data
    of the removed users.
    """

    __exporter: ResultsExporter
    __export_data: ExportData
    __filter: Filter[RemovedUser]

    def __init__(
        self,
        exporter: ResultsExporter,
        export_data: ExportData,
        filter_: Filter,
    ) -> None:
        super().__init__()
        self.__exporter = exporter
        self.__export_data = export_data
        self.__filter = filter_

    async def create_or_update(
        self,
        removed_user: RemovedUser | None,
        username: str,
        ad_user: ADUser | None,
        results: list[dict[str, object]],
        catalog_number: str,
        error_count: int,
        warning_count: int,
        total_time: float,
    ) -> RemovedUser:
        """Create or update a removed user.

        Parameters
        ----------
        removed_user : RemovedUser | None
            Removed user if exists.
        username : str
            Username of the removed user.
        ad_user : ADUser | None
            Active Directory user data if found.
        results : list[dict[str, object]]
            Results of the automation.
        catalog_number : str
            Catalog number of the removed user.
        error_count : int
            Error count of the automation.
        warning_count : int
            Warning count of the automation.
        total_time : float
            Total time of the automation.

        Returns
        -------
        RemovedUser
            Created/updated removed user.

        """
        if removed_user:
            await removed_user.set(
                {
                    str(RemovedUser.data): results,
                    str(RemovedUser.catalogNumber): catalog_number,
                    str(RemovedUser.errorCount): error_count,
                    str(RemovedUser.warningCount): warning_count,
                    str(RemovedUser.time): total_time,
                }
            )
        else:
            removed_user = RemovedUser(
                username=username,
                ad_user=ad_user,
                data=results,
                catalogNumber=catalog_number,
                errorCount=error_count,
                warningCount=warning_count,
                time=total_time,
            )
            await removed_user.create()
        return removed_user

    async def list(
        self,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[RemovedUserResponseDto]:
        """Get the list of removed users.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the removed users, by default None.

        Returns
        -------
        Pagination[RemovedUserResponseDto]
            Paginated list of removed users.

        """
        query = RemovedUser.find(RemovedUser.isDeleted == False)  # noqa: E712

        if filter_data is not None:
            query = self.__filter.create(filter_data, query)

        pagination = Pagination[RemovedUserResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = RemovedUserResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def retrieve(self, id: str) -> RemovedUserResponseDto | None:
        """Find a removed user by id.

        Parameters
        ----------
        id : str
            Removed user id.

        Returns
        -------
        RemovedUserResponseDto | None
            Removed user if found.

        """
        removed_user = await RemovedUser.find_one(
            RemovedUser.id == PydanticObjectId(id)
        )
        if not removed_user:
            return None
        return RemovedUserResponseDto.from_orm(removed_user)

    async def retrieve_by_username(
        self, username: str
    ) -> RemovedUserResponseDto | None:
        """Find a removed user by username.

        Parameters
        ----------
        username : str
            Removed user username.

        Returns
        -------
        RemovedUserResponseDto | None
            Removed user if found.

        """
        removed_user = await RemovedUser.find_one(
            RemovedUser.username == username.strip().upper()
        )
        if not removed_user:
            return None
        return RemovedUserResponseDto.from_orm(removed_user)

    @asyncify
    def generate_pdf_file(self, removed_user: RemovedUser) -> File | None:
        """Generate a PDF file from the removed user data.

        Parameters
        ----------
        removed_user : RemovedUser
            Removed user.

        Returns
        -------
        File | None
            Generated PDF file.

        """
        pdf_content = self.__exporter.to_pdf(
            removed_user,
            action='remove',
            has_privileges=False,
            include_not_found=True,
            include_errors=True,
        )
        if pdf_content is None:
            return None
        date = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
        filename = f'reporte_{removed_user.username}_{date}'
        return self.__export_data.stream_to_file(
            stream=pdf_content, filename=filename, extension='pdf'
        )

    async def export(self, id: str) -> File | None:
        """Find a removed user by its id and export it.

        Parameters
        ----------
        id : str
            Id of the removed user.

        Returns
        -------
        File | None
            Exported removed user to PDF if found.

        """
        removed_user = await RemovedUser.find_one(
            RemovedUser.id == PydanticObjectId(id)
        )
        if not removed_user:
            return None
        return await self.generate_pdf_file(removed_user)

    async def export_by_username(self, username: str) -> File | None:
        """Find a removed user by its username and export it.

        Parameters
        ----------
        username : str
            Username of the removed user.

        Returns
        -------
        File | None
            Exported removed user to PDF if found.

        """
        removed_user = await RemovedUser.find_one(
            RemovedUser.username == username.strip().upper()
        )
        if not removed_user:
            return None
        return await self.generate_pdf_file(removed_user)
