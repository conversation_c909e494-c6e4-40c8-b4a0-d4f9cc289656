//#region imports
import { Application } from 'src/lib/interfaces';
//#endregion

//#region types
export interface ADUserData {
  sam_account_name?: string;
  user_principal_name?: string;
  first_name?: string;
  last_name?: string;
  fullname?: string;
  document?: string;
  mail?: string;
  active: boolean;
  member_of?: string[];
  time: number;
}

export interface ConsultedUser {
  id: string;
  username: string;
  exp: Date
  ad_user?: ADUserData;
  data: Application[];
  time: number;
  catalogNumber?: string;
  errorCount?: number;
  warningCount?: number;
  updatedAt: Date;
  createdAt: Date;
}

export interface ConsultedUserResponse {
  id: string;
  username: string;
  exp: string;
  ad_user?: ADUserData;
  data: Application[];
  time: number;
  catalogNumber?: string;
  errorCount?: number;
  warningCount?: number;
  updatedAt: string;
  createdAt: string;
}
//#endregion

export class ConsultedUserTools {
  public static load(consultedUserResponse: ConsultedUserResponse): ConsultedUser {
    return {
      id: consultedUserResponse.id,
      username: consultedUserResponse.username,
      exp: new Date(consultedUserResponse.exp),
      ad_user: consultedUserResponse.ad_user,
      data: consultedUserResponse.data,
      time: consultedUserResponse.time,
      catalogNumber: consultedUserResponse.catalogNumber,
      errorCount: consultedUserResponse.errorCount,
      warningCount: consultedUserResponse.warningCount,
      createdAt: new Date(consultedUserResponse.createdAt),
      updatedAt: new Date(consultedUserResponse.updatedAt),
    }
  }

  public static loadMany(consultedUserResponse: ConsultedUserResponse[]): ConsultedUser[] {
    return consultedUserResponse.map((consultedUserResponse: ConsultedUserResponse): ConsultedUser => ConsultedUserTools.load(consultedUserResponse));
  }

  public static getEmptyConsultedUser(): ConsultedUserResponse {
    return {
      id: '',
      username: '',
      exp: '',
      data: [],
      time: 0.0,
      updatedAt: '',
      createdAt: '',
    }
  }
}
