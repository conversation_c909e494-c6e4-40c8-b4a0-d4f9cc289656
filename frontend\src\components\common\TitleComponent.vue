<template>
  <div class="row wrap">
    <div class="row items-center">
      <q-btn v-if="withHomeButton" class="q-pa-sm q-mr-sm q-mb-xs" round color="primary" size="sm" icon="home" @click="() => router.push('/')">
        <q-tooltip class="text-body2" :offset="[10, 10]">
          {{t('returnHome')}}
        </q-tooltip>
      </q-btn>
    </div>
    <div style="display: inline-block">
      <div class="title">
        {{ props.title }}
      </div>
      <div v-if="props.description" class="col-12">
        {{ props.description }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
//#endregion

//#region types
export interface TitleConfig {
  [key: string]: unknown;
}

export interface TitleProps {
  title: string;
  withHomeButton?: boolean;
  description?: string;
  icon?: string;
  config?: TitleConfig;
  colNumbersFromComponent?: string;
}
//#endregion

const { t } = useI18n();

const router = useRouter();

const props = withDefaults(defineProps<TitleProps>(), {
  withHomeButton: true
});
</script>
