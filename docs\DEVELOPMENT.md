<!-- omit in toc -->
# Guía de Desarrollo - Bot Consulta de Usuarios

Esta guía técnica describe la arquitectura, estructura del código y mejores prácticas para el desarrollo y mantenimiento del **Bot Consulta de Usuarios**. Está dirigida a desarrolladores que necesiten entender, modificar o extender la funcionalidad del sistema.

## Información General

El Bot Consulta de Usuarios es una plataforma RPA (Robotic Process Automation) que automatiza la consulta y retiro de usuarios en **30 aplicaciones empresariales** diferentes. Utiliza una arquitectura de microservicios basada en contenedores Docker.

### Tecnologías Principales
- **Backend**: Python 3.10+ con Starlette (ASGI)
- **Frontend**: Vue.js 3 + Quasar Framework 2
- **Base de Datos**: MongoDB 8.0+
- **Autenticación**: Node.js con integración SEUS 4
- **Containerización**: <PERSON><PERSON> + <PERSON>er Compose
- **Proxy**: Nginx para balanceo de carga

<!-- omit in toc -->
## Tabla de Contenidos
- [Información General](#información-general)
    - [Tecnologías Principales](#tecnologías-principales)
- [Arquitectura del Sistema](#arquitectura-del-sistema)
    - [Componentes Principales](#componentes-principales)
    - [Estructura del Proyecto](#estructura-del-proyecto)
    - [Diagrama de Arquitectura](#diagrama-de-arquitectura)
- [Desarrollo del Backend](#desarrollo-del-backend)
    - [Arquitectura de la API](#arquitectura-de-la-api)
    - [Estructura del Backend (bot/)](#estructura-del-backend-bot)
    - [Flujo de Ejecución de una Petición](#flujo-de-ejecución-de-una-petición)
        - [1. Recepción de Petición](#1-recepción-de-petición)
        - [2. Validación (Opcional)](#2-validación-opcional)
        - [3. Procesamiento en Servicios](#3-procesamiento-en-servicios)
        - [4. Ejecución de Módulos](#4-ejecución-de-módulos)
        - [5. Persistencia de Datos](#5-persistencia-de-datos)
    - [Diagrama de Flujo Detallado](#diagrama-de-flujo-detallado)
    - [Documentación del Código](#documentación-del-código)
    - [Script principal](#script-principal)
    - [Inyección de dependencias](#inyección-de-dependencias)
        - [Explicación de la inyección de dependencias](#explicación-de-la-inyección-de-dependencias)
        - [Módulo de inyecciones](#módulo-de-inyecciones)
        - [Hacer que un elemento pueda ser inyectable](#hacer-que-un-elemento-pueda-ser-inyectable)
    - [Configuración](#configuración)
    - [Middlewares](#middlewares)
    - [DTOs](#dtos)
        - [Definición](#definición)
        - [Uso](#uso)
    - [Controladores](#controladores)
    - [Validadores](#validadores)
    - [Modelos](#modelos)
    - [Servicios](#servicios)
    - [Módulos de Automatización](#módulos-de-automatización)
        - [Definición y Propósito](#definición-y-propósito)
        - [Aplicaciones Soportadas (30 Módulos)](#aplicaciones-soportadas-30-módulos)
        - [Estructura de un Módulo](#estructura-de-un-módulo)
        - [Métodos Requeridos](#métodos-requeridos)
        - [Manejo de Errores](#manejo-de-errores)
        - [Método de retiro de usuarios](#método-de-retiro-de-usuarios)
        - [Tipo de retorno](#tipo-de-retorno)
- [Guías de Desarrollo](#guías-de-desarrollo)
    - [Agregar un Nuevo Módulo de Automatización](#agregar-un-nuevo-módulo-de-automatización)
        - [1. Crear el Módulo](#1-crear-el-módulo)
        - [2. Crear el DTO de Respuesta](#2-crear-el-dto-de-respuesta)
        - [3. Registrar el Módulo](#3-registrar-el-módulo)
        - [4. Configurar en el Servicio de Automatización](#4-configurar-en-el-servicio-de-automatización)
        - [5. Agregar Configuración](#5-agregar-configuración)
        - [6. Agregar Traducciones](#6-agregar-traducciones)
    - [Crear un Nuevo Endpoint](#crear-un-nuevo-endpoint)
        - [1. Crear el Controlador](#1-crear-el-controlador)
        - [2. Registrar el Controlador](#2-registrar-el-controlador)
    - [Configurar Variables de Entorno](#configurar-variables-de-entorno)
        - [Estructura Recomendada](#estructura-recomendada)
        - [Leer en Configuración](#leer-en-configuración)
- [Testing y Debugging](#testing-y-debugging)
    - [Ejecutar Tests](#ejecutar-tests)
    - [Debugging de Módulos](#debugging-de-módulos)
        - [1. Usar el Script de Prueba](#1-usar-el-script-de-prueba)
        - [2. Logs Detallados](#2-logs-detallados)
        - [3. Usar el Cliente REST](#3-usar-el-cliente-rest)
    - [Mejores Prácticas](#mejores-prácticas)
        - [Desarrollo de Módulos](#desarrollo-de-módulos)
        - [Seguridad](#seguridad)
        - [Performance](#performance)
    - [Traducción con I18N](#traducción-con-i18n)
        - [Uso](#uso-1)
        - [Agregar nuevo lenguaje](#agregar-nuevo-lenguaje)
        - [Mensajes con parámetros](#mensajes-con-parámetros)
    - [Librerías](#librerías)
        - [Definición](#definición-1)
        - [Descripción de las librerías](#descripción-de-las-librerías)
    - [Librería `http`](#librería-http)
    - [Librería `pdf`](#librería-pdf)
        - [Ejemplo de uso](#ejemplo-de-uso)
        - [Métodos](#métodos)
        - [PDF de ejemplo](#pdf-de-ejemplo)
    - [Librería `validator`](#librería-validator)
        - [Ejemplo de uso](#ejemplo-de-uso-1)
        - [Descripción de los validadores](#descripción-de-los-validadores)
        - [Descripción de los operadores](#descripción-de-los-operadores)

## Arquitectura del Sistema

### Componentes Principales

El sistema está compuesto por **6 servicios principales** que trabajan en conjunto:

| Servicio | Tecnología | Puerto | Función |
|----------|------------|--------|---------|
| **frontend** | Vue.js 3 + Quasar | 8090 | Interfaz de usuario web |
| **bot_proxy** | Nginx | 8011 | Proxy reverso y balanceador de carga |
| **bot_consult** | Python + Starlette | 8010 | API para consulta de usuarios |
| **bot_remove** | Python + Starlette | 8010 | API para retiro de usuarios |
| **auth** | Node.js + Express | 3000 | Servicio de autenticación SEUS 4 |
| **db** | MongoDB | 27017 | Base de datos principal |

### Estructura del Proyecto

```
bot_consultausuarios/
├── bot/                    # 🐍 Backend Python (API REST)
│   ├── controllers/        # Endpoints de la API
│   ├── services/          # Lógica de negocio
│   ├── models/            # Modelos de MongoDB
│   ├── modules/           # Módulos de automatización (30 apps)
│   ├── lib/               # Librerías y utilidades
│   ├── dtos/              # Data Transfer Objects
│   ├── validators/        # Validadores de datos
│   ├── middlewares/       # Middlewares personalizados
│   ├── i18n/              # Sistema de traducción
│   └── main.py            # Punto de entrada de la aplicación
├── frontend/              # 🌐 Frontend Vue.js + Quasar
│   ├── src/
│   │   ├── components/    # Componentes Vue reutilizables
│   │   ├── pages/         # Páginas de la aplicación
│   │   ├── layouts/       # Layouts de la aplicación
│   │   ├── services/      # Servicios para comunicación con API
│   │   └── stores/        # Gestión de estado con Pinia
│   └── public/            # Archivos estáticos
├── auth/                  # 🔐 Servicio de autenticación Node.js
│   └── src/index.js       # Servidor de autenticación SEUS 4
├── bot_proxy/             # 🔄 Proxy Nginx
│   ├── default.conf       # Configuración de producción
│   └── default.dev.conf   # Configuración de desarrollo
├── cert/                  # 🔒 Certificados SSL
│   ├── cert.pem           # Certificado público
│   └── key.pem            # Clave privada
├── docs/                  # 📚 Documentación
├── commands/              # 🛠️ Herramientas de línea de comandos
│   └── env_encrypter.py   # Encriptador de archivos .env
├── docker-compose.yml     # 🐳 Configuración Docker producción
├── docker-compose.dev.yml # 🐳 Configuración Docker desarrollo
├── .env.example           # 📝 Plantilla de configuración
└── README.md              # 📖 Documentación principal
```

### Diagrama de Arquitectura

![Arquitectura del Sistema](/docs/screenshots/development_architecture.png)

La arquitectura sigue el patrón de **microservicios** con las siguientes características:

- **Separación de responsabilidades**: Cada servicio tiene una función específica
- **Escalabilidad horizontal**: Múltiples instancias del bot para consulta y retiro
- **Balanceo de carga**: Nginx distribuye las peticiones entre instancias
- **Seguridad**: Autenticación centralizada y comunicación HTTPS
- **Persistencia**: MongoDB para almacenamiento de resultados y transacciones

## Desarrollo del Backend

### Arquitectura de la API

El backend utiliza **Starlette** como framework ASGI y sigue una arquitectura en capas bien definida:

```
📱 Cliente (Frontend/API)
    ↓
🌐 Controladores (Endpoints)
    ↓
✅ Validadores (Opcional)
    ↓
⚙️ Servicios (Lógica de Negocio)
    ↓
🤖 Módulos (Automatización) ← → 🗄️ Modelos (Base de Datos)
    ↓
🏢 Aplicaciones Externas (30 sistemas)
```

### Estructura del Backend (bot/)

| Directorio | Propósito | Archivos Clave |
|------------|-----------|----------------|
| **controllers/** | Endpoints de la API REST | `automation_controller.py`, `consulted_user_controller.py` |
| **services/** | Lógica de negocio y orquestación | `automation_service.py`, `consulted_user_service.py` |
| **models/** | Modelos de datos MongoDB | `consulted_user.py`, `removed_user.py`, `transaction.py` |
| **modules/** | Módulos de automatización RPA | 30 módulos (uno por aplicación) |
| **lib/** | Librerías y utilidades | `http/`, `pdf/`, `validator/`, configuración |
| **dtos/** | Data Transfer Objects | Estructuras de petición y respuesta |
| **validators/** | Validadores de entrada | Validación de datos de peticiones |
| **middlewares/** | Middlewares personalizados | `language_middleware.py` |
| **i18n/** | Sistema de internacionalización | Traducciones en español |

### Flujo de Ejecución de una Petición

#### 1. Recepción de Petición
```python
# Ejemplo: POST /automation/consult
@auth(AuthRole.CONSULT)
@post('/automation/consult')
async def consult_user(self, data: AutomationRequestDto,
                      service: AutomationService) -> DtoResponse[TransactionResponseDto]:
```

#### 2. Validación (Opcional)
```python
# Los validadores verifican la estructura y contenido de los datos
validator = AutomationValidator()
if not await validator.validate(data):
    return ErrorResponse(validator.error)
```

#### 3. Procesamiento en Servicios
```python
# El servicio orquesta la lógica de negocio
class AutomationService:
    async def consult_user(self, data: AutomationRequestDto):
        # 1. Crear transacción para seguimiento
        transaction = await self._create_transaction(data)

        # 2. Consultar Active Directory
        ad_result = await self._consult_active_directory(data.username)

        # 3. Ejecutar módulos de automatización
        results = await self._run_automation_modules(data.username)

        # 4. Guardar resultados en base de datos
        consulted_user = await self._save_results(results)

        return consulted_user
```

#### 4. Ejecución de Módulos
```python
# Cada módulo implementa la interfaz estándar
class SaludWebModule(BaseModule):
    async def consult_user(self, username: str) -> SaludWebResponseDto:
        # Lógica específica para consultar Salud Web
        return user_data
```

#### 5. Persistencia de Datos
```python
# Los modelos manejan la persistencia en MongoDB
class ConsultedUser(BaseModel):
    username: str
    ad_user: ADUser | None
    data: list[dict[str, Any]]
    time: float = 0.0
```

### Diagrama de Flujo Detallado

![Diagrama del flujo de ejecución del Bot](/docs/screenshots/development_bot_execution_flow.png)

### Documentación del Código

> **💡 Tip**: Todo el código está documentado con **docstrings** detallados. En Visual Studio Code, coloque el cursor sobre cualquier clase o método para ver su documentación:
>
> ![Docstring](/docs/screenshots/development_docstring.png)

### Script principal
Es el archivo **main.py** el cual contiene el código necesario para iniciar una instancia de Starlette (un servidor ASGI).

Entre las modificaciones importantes a realizar en este archivo se encuentra la importación de los modelos para ser inicializados por la librería Beanie y el motor de la base de datos (MongoDB):

```python
# bot/main.py

from models import CustomModel1, CustomModel2, ... # <----- Importar modelos
...

async def on_startup():
    ...
    await init_beanie(
        database=client[database],
        document_models=[
            CustomModel1, #  <----- Inicializar modelos
            CustomModel2,
        ])
```

Otra posible modificación puede ser el cambio de puerto en caso de que se requiera:

```python
# bot/main.py

if __name__ == '__main__':
    uvicorn.run(
        app='main:app',
        host='0.0.0.0',
        port=8010, # <----------- Puerto de la API
        log_level='info',
        reload=False if Config.production else True
    )
```

### Inyección de dependencias

#### Explicación de la inyección de dependencias
La API implementa inyección de dependencias mediante los métodos "**singleton**", "**scoped**" y "**transient**".

| Método | Descripción |
| ---- | ---- |
| Singleton | Se crea una única instancia para todas las peticiones |
| Scoped | Se crea una instancia por cada petición |
| Transient | Se crea una instancia nueva siempre |

Los módulos, servicios y validadores deben ser inyectados por temas de optimización y buenas prácticas. Inyectar o no las librerías depende de la necesidad.

#### Módulo de inyecciones
Esta inyección se hace en el archivo [**injectables.py**](/bot/lib/injectables.py) en el cual se importa el elemento a inyectar (módulo, servicio, validador, etc.) y se inyecta usando los métodos anteriormente mencionados:

```python
# bot/lib/injectables.py

from services import CustomService # <-------

class Injectables:

    @staticmethod
    def execute():
        Injectables.config()
        ...
        Injectables.services()
        ...
        injectable.end()

    @staticmethod
    def services():
        injectable.add_scoped(CustomService) # <-------
```

#### Hacer que un elemento pueda ser inyectable
Todos los elementos que vayan a ser inyectados deben heredar de la clase "**BaseInjectable**", por ejemplo:
```python
from lib.base_injectable import BaseInjectable

class MiInyectable(BaseInjectable)
    pass
```

La forma en la que se suelen inyectar los elementos es la siguiente:

| Elemento | Método de inyección |
| ---- | ---- |
| Módulos de configuración | Singleton |
| Conexión a bases de datos | Singleton |
| Módulos de automatización | Scoped |
| Módulos de conexiones | Singleton o Scoped |
| Módulos de traducción (I18N) | Transient |
| Servicios | Scoped |
| Validadores | Scoped |
| Librerías | Transient |

### Configuración
En el módulo [**config**](/bot/lib/config.py) se leen las variables de entorno
del archivo **.env**. También, se instancia un objeto de la clase **Health**,
el cual es utilizado para verificar el estado de la API.

La lectura de las variables de entorno se realiza así:
```python
# bot/lib/config.py

from starlette.config import Config

_config = Config()

PROD = _config('PROD', cast=bool, default=False)
```

El primer parámetro es el nombre de la variable de entorno, el segundo es el
tipo de dato (debe ser una clase o una función que retorne el tipo de dato deseado)
y el tercero es el valor por defecto.

Utilice la clase **Secret** como el tipo de dato para variables sensibles como
contraseñas, tokens, etc., así:

```python
# bot/lib/config.py

from starlette.config import Config
from starlette.datastructures import Secret

_config = Config()

DATABASE_PASSWORD = _config('DATABASE_PASSWORD', cast=Secret, default='')
```

Si no especifica un valor por defecto, se entiende que la variable de entorno es
obligatoria, es decir, si no se especifica un valor por defecto y la variable de
entorno no está definida, se lanzará una excepción.

### Middlewares
Para agregar un middleware siga los siguientes pasos:

1. Diríjase a la carpeta "**middlewares**" y cree un archivo con una clase basada en _BaseMiddleware_:

```python
# bot/middlewares/custom_middleware.py

from lib.base_middleware import BaseMiddleware


class CustomMiddleware(BaseMiddleware):

    async def before_dispatch(self) -> None:
        ...

    async def after_dispatch(self) -> None:
        ...
```

Para conocer como funciona un middleware, consulte la [documentación de Starlette acerca de los middlewares](https://www.starlette.io/middleware/) y/o revise la documentación de la clase [BaseMiddleware](/bot/lib/base_middleware.py).

2. Agregue el middleware al **\_\_init\_\_.py** de la carpeta middlewares:
```python
# bot/middlewares/__init__.py

...
from .custom_middleware import CustomMiddleware # <-------
```

3. Importe el middleware en el script principal (**main.py**) y agréguelo a la lista de middlewares:

```python
# bot/main.py

from middlewares import ..., CustomMiddleware # <-------

...

middleware = [
    Middleware(
        TrustedHostMiddleware,
        allowed_hosts=['*'],
    ),
    ...
    Middleware(LanguageMiddleware),
    Middleware(CustomMiddleware) # <-------
]
```

### DTOs

#### Definición
Los Data Transfer Objects, son las clases que representan la estructura del cuerpo de las peticiones y respuestas en los endpoints, es decir, representan un JSON como un objeto y viceversa.

Se crean en la carpeta "**dtos**" y hay tres tipos, genéricos, de petición y de respuesta.

1. DTO genérico:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseDto # <---- Clase base de los DTOs

class CustomDto(BaseDto):
    fullName: str
    lastName: str
    ...
```

Suelen usarse para almacenar datos de un objeto con mayor facilidad y seguridad. Aunque también sirven para almacenar campos de tipo objeto en un [modelo](#modelos).

2. DTO de petición:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseRequestDto # <---- DTO de petición

class CustomRequestDto(BaseRequestDto):
    fullName: str
    lastName: str
    ...
```

El cuerpo de la petición debería ser el siguiente:
```json
{
    "fullName": "María",
    "lastName": "Hernandez"
}
```

3. DTO de respuesta:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseResponseDto # <---- DTO de respuesta

class CustomResponseDto(BaseResponseDto):
    id: PydanticObjectId
    fullName: str
    lastName: str
    ...
    updatedAt: datetime.datetime
    createdAt: datetime.datetime
```

El cuerpo de la respuesta debería ser el siguiente:
```json
{
    "id": "Bf987892347234",
    "fullName": "María",
    "lastName": "Hernandez",
    ...
    "updatedAt": "2024-05-30 14:20:40.324243",
    "createdAt": "2024-05-30 14:20:40.324243"
}
```

#### Uso

Los DTOs poseen 3 funciones importantes:

1. **to_dict**: Convierte un DTO a diccionario.
   ```python
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    dto = MyDTO(name='Foo', surname='Bar', age=22)
    > dto.to_dict()
   ```

    El resultado sería el siguiente:
    ```python
    {'name': 'Foo', 'surname': 'Bar', 'age': 22}
    ```

2. **from_orm**: Convierte una instancia de un modelo a DTO.
    ```python

    # Modelo
    class MyModel(BaseModel):
        name: str
        surname: str
        age: int

    # DTO
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    model = await MyModel.find_one(MyModel.name == 'Foo')
    dto = MyDTO.from_orm(model)
    > dto.name
    ```

    Resultado:
    ```python
    Foo
    ```

3. **from_orm_many**: Convierte una lista de instancias de un modelo a una lista de DTOs.
    ```python

    # Modelo
    class MyModel(BaseModel):
        name: str
        surname: str
        age: int

    # DTO
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    models = await MyModel.find().to_list()
    dtos = MyDTO.from_orm_many(models)

    for dto in dtos:
        print(dto.name)
    ```

    Resultado:
    ```python
    Foo
    Bar
    Baz
    ...
    ```

NOTA: La explicación de como se usan los DTOs de petición y de respuesta está en el siguiente apartado que habla sobre los [controladores](#controladores) la cual se encuentra a continuación.

### Controladores
Son los endpoints de la API. Para crear un controlador, siga los siguientes pasos:

1. Si el endpoint espera recibir datos, cree un DTO como se explica [aquí](#dtos).

2. Cree un controlador en la carpeta "**controllers**" como el siguiente:

```python
# bot/controllers/custom_controller.py

from lib.auth import auth
from lib.base_exception import BaseException
from lib.base_controller import BaseController, get, post
...

from dtos.custom_dto import CustomRequestDto, CustomResponseDto # <--- DTOs

class CustomController(BaseController): # <--- Heredar de BaseController

    @auth # <------- El cliente debe estar autenticado
    @post('/create') # <------- La petición es POST a la ruta /create
    async def create(self,
        data: CustomRequestDto, # <----- DTO de petición
        validator: CustomValidador # <---- Inyecta el validador
        # también podría inyectar un servicio u otro elemento
    ) -> DtoResponse[CustomResponseDto] | ErrorResponse:
        error = ErrorValidator()
        try:
            if not await validator.validate(data): # Validar datos

                # Retornar error si los datos no son validos
                return ErrorResponse(validator.error)

            # Ejecutar servicio
            created_data = await validator.service.create(data):

            # Retornar respuesta como DTO
            return DtoResponse[CustomResponseDto](created_data)
        except BaseException as e:
            error.add('common', str(e))
            return ErrorResponse(error, e.code)

    @auth # <------- El cliente debe estar autenticado
    @get('/list') # <------- La petición es GET a la ruta /list
    async def list(self) -> PaginationResponse[CustomResponseDto] | ErrorResponse:
        ...
```

Como puede observar en el ejemplo anterior, el controlador hereda de la clase "**BaseController**" y cada método (endpoint) implementa un decorador que define el método y la ruta del endpoint (POST, GET, DELETE, PUT). Estos se importan con:
```python
from lib.base_controller import BaseController, get, post, delete, put
```

El decorador "**auth**" se usa cuando se requiere que en la petición haya un header Authorization con un token válido, es decir, que el cliente se haya autenticado.
Este se importa con:
```python
from lib.auth import auth
```

Para mayor información sobre la autenticación consulte la [documentación de Starlette sobre el middleware de autenticación](https://www.starlette.io/authentication/).

3. Cuando el controlador esté creado, importe el controlador en el **\_\_init\_\_.py** de la carpeta controllers:
```python
# bot/controllers/__init__.py

...
from .custom_controller import CustomController # <-----
```

### Validadores
Validan los DTOs de los controladores. Se inyectan como se mencionó en la sección de [inyección de dependencias](#inyección-de-dependencias) y se ubican en la carpeta "**validators**".

Su estructura es la siguiente:
```python
# bot/validators/custom_validator.py

class CustomValidator(BaseInjectable, BaseValidator):

    service: CustomService # <----- Servicio (opcional, también podría ser un módulo)

    def __init__(self,

        # Puede inyectar el servicio aquí o en el controlador, es su elección
        service: CustomService,

        # SIEMPRE INYECTE EL I18N, es requerido por la librería de validación
        i18n: I18N
    ) -> None:
        self.service = service
        self.i18n = i18n

    def validator(self,
        custom_dto: CustomRequestDto # <------- DTO
    ) -> DataValidator:
        validator = DataValidator()

        # Agregar validación de "requerido" para el campo "fullName" del dto
        validator.add('fullName', Validator[str](custom_dto.fullName).required())
        ...

        return validator

    async def validate(self,
        custom_dto: CustomRequestDto # <------- DTO
    ) -> bool:

        # No cambie estas líneas de código a no ser que lo necesite
        validator = self.validator(custom_dto)
        if await validator.validate():
            return True
        self.error = validator.get_error(self.i18n)
        return False
```

### Modelos
Representan las colecciones de la base de datos (en este caso, MongoDB). Proporcionan métodos para realizar consultas y operaciones en una colección de la base de datos como insertar, actualizar, listar, eliminar, entre otras.

Se importan en el script principal (**main.py**) como se explicó [en esta sección](#script-principal) y se ubican en la carpeta "**models**".

La estructura base de un modelo es la siguiente:
```python
# bot/models/custom_model.py

from lib.base_model import BaseModel
from lib.base_dto import BaseDto

class User(BaseDto): # <-- Para campos de tipo objeto se hereda la clase BaseDto
    username: str
    document: str

class CustomModel(BaseModel): # <-- Los modelos heredan la clase BaseModel
    text: str
    user: User # <-- Campos de tipo objeto
    state: Literal['done', 'error']
    ...

    # (OPCIONAL) Clase para implementar proyecciones de Mongo
    class ProjectionConfig:

        # Agregar campo "username" extraído del campo "user"
        username = 'user.username'

        # Agregar campo "document" extraído del campo "user"
        document = 'user.document'

    # (OPCIONAL) Clase para implementar traducción al exportar datos
    class TranslationConfig:

        # Traducir nombres de las columnas (sólo para Excel)
        columns = {
            'text': '<mensaje de i18n>',
            'username': '<mensaje de i18n>',
            'document': '<mensaje de i18n>',
            'createdAt': '<mensaje de i18n>',
            'updatedAt': '<mensaje de i18n>'
        }

        # Traducir valores de un campo
        data = {
            'status': {
                'done': '<mensaje de i18n>',
                'error': '<mensaje de i18n>'
            },
        }
```

NOTA: Las clases ProjectionConfig y TranslationConfig son totalmente opcionales.

Al implementar la clase ProjectionConfig, debe usar la librería [Query](/bot/lib/query.py) para hacer uso de esta proyección. Un ejemplo del uso es este:

```python
# Consultar documentos de la colección del modelo "CustomModel"
# cuyo campo isDeleted sea "False".
query = self.__query.create(CustomModel, CustomModel.isDeleted == False)

# Listar documentos
documents = await query.list()
...
```

NOTA: Si está usando el linter **Ruff**, para ocultar la advertencia de comparaciones con valores booleanos (`True` y `False`) con el operador `==`, agregue el siguiente comentario al final de la línea de código donde se encuentra la comparación:

```python
query = self.__query.create(..., CustomModel.isDeleted == False)  # noqa: E712
```

Si desea conocer el funcionamiento de las proyecciones de Mongo [haga click aquí para ir a la documentación de Mongo acerca de las proyecciones](https://www.mongodb.com/docs/manual/reference/operator/projection/positional/).

Si implementa la clase TranslationConfig, debe agregar los mensajes I18N a utilizar para las traducciones como se menciona [en la sección de traducción con I18N](#traducción-con-i18n).

> ## <span style="color:red">¡Importante!</span>
> Por seguridad y buenas prácticas, los controladores (endpoints) no deben interactuar con los modelos, es decir, no deben realizar ningún tipo de operación con ellos.<br>
> Los controladores solo deben recibir peticiones y enviar respuestas, por lo tanto, siempre que necesite retornar una instancia o una lista de instancias de un modelo, utilice las funciones [**from_orm**](#uso) y [**from_orm_many**](#uso) para retornar dichas instancias como un DTO o lista de DTOs como se explicó en la sección sobre [DTOs](#dtos).
> <br><br>

### Servicios

Reciben los datos de los endpoints y se comunican con los modelos para hacer consultas en la base de datos o con los módulos para realizar automatizaciones o algún otro tipo de operación (por ejemplo, exportar datos). Se inyectan como se mencionó [aquí](#inyección-de-dependencias) y se ubican en la carpeta "**services**".

Su estructura es la siguiente:

```python
# bot/services/custom_service.py

from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService

class CustomService(
        BaseInjectable, BaseService): # Los servicios heredan de estas dos clases

    __filter: Filter
    __query: Query
    ...

    def __init__(self,

        # Estas librerías están inyectadas como dependencias
        _filter: Filter,
        query: Query
    ) -> None:
        super().__init__()
        self.__filter = _filter
        self.__query = query

    # Ejemplo de una función para crear un documento en una colección
    async def create(self, custom_dto: CustomRequestDto) -> CustomResponseDto:
        new: Modelo = Modelo(**custom_dto.to_dict())
        return CustomResponseDto.from_orm(await new.create())
    ...
```

### Módulos de Automatización

#### Definición y Propósito

Los **módulos de automatización** son el corazón del sistema RPA. Cada módulo implementa la lógica específica para interactuar con una aplicación empresarial, simulando las acciones que realizaría un usuario humano.

#### Aplicaciones Soportadas (30 Módulos)

El sistema actualmente soporta **30 aplicaciones diferentes**:

| # | Módulo | Aplicación | Función Principal |
|---|--------|------------|-------------------|
| 1 | `AgendaWebModule` | Agenda Web | Gestión de citas y agenda médica |
| 2 | `AribaModule` | Ariba | Plataforma de compras y proveedores |
| 3 | `BandejaEscalamientoModule` | Bandeja Escalamiento | Sistema de escalamiento de casos |
| 4 | `BeyondHealthModule` | Beyond Health | Plataforma de salud digital |
| 5 | `BillingCenterModule` | Billing Center | Centro de facturación |
| 6 | `CAServiceDeskModule` | CA Service Desk | Mesa de servicio TI |
| 7 | `CaseTrackingModule` | Case Tracking | Seguimiento de casos |
| 8 | `ClaimCenterModule` | Claim Center | Centro de reclamaciones |
| 9 | `ConfluenceModule` | Confluence | Wiki corporativo |
| 10 | `ContactManagerModule` | Contact Manager | Gestión de contactos |
| 11 | `ConveniosModule` | Convenios | Sistema de convenios |
| 12 | `EventosAdversosModule` | Eventos Adversos | Reporte de eventos adversos |
| 13 | `HealthCloudModule` | Health Cloud | Salesforce Health Cloud |
| 14 | `IDMModule` | IDM | Identity Management |
| 15 | `IntegradorModule` | Integrador | Sistema integrador |
| 16 | `IPSAModule` | IPSA | Intercambios IPSA |
| 17 | `OfficeModule` | Office 365 | Microsoft Office 365 |
| 18 | `OHIModule` | OHI | Oracle Health Insurance |
| 19 | `OIPAModule` | OIPA | Oracle Insurance Policy Administration |
| 20 | `PolicyCenterModule` | Policy Center | Centro de pólizas |
| 21 | `PorfinModule` | Porfin | Sistema financiero Porfin |
| 22 | `SalesforceModule` | Salesforce | CRM Salesforce |
| 23 | `SaludWebModule` | Salud Web | Portal web de salud |
| 24 | `SEEEModule` | SEEE | Sistema SEEE |
| 25 | `SEUSModule` | SEUS 4 | Sistema de seguridad SEUS |
| 26 | `SOATModule` | SOAT | Sistema SOAT |
| 27 | `STARModule` | STAR | Sistema STAR |
| 28 | `TablaTercerosModule` | Tabla Terceros | Gestión de terceros |
| 29 | `ViafirmaModule` | Viafirma | Plataforma de firma digital |
| 30 | `ActiveDirectory` | Active Directory | Directorio activo corporativo |

#### Estructura de un Módulo

Todos los módulos siguen la misma estructura base:

```python
# bot/modules/ejemplo_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule
from lib.exceptions import NotFoundError
from dtos.automation_dto import EjemploResponseDto

class EjemploModule(BaseInjectable, BaseModule):
    """Módulo para consultar la aplicación Ejemplo."""

    def __init__(self, i18n: I18N) -> None:
        super().__init__()
        self.i18n = i18n
        self.session = TLSSession()

    def consult_user(self, username: str) -> EjemploResponseDto:
        """Consulta un usuario en la aplicación.

        Parameters
        ----------
        username : str
            Nombre de usuario a consultar

        Returns
        -------
        EjemploResponseDto
            Datos del usuario consultado
        """
        try:
            self._authenticate()
            user_data = self._fetch_user_data(username)
            return self._create_response_dto(user_data)
        finally:
            self.close_session()

    def remove_user(self, username: str) -> EjemploRemovedUserResponseDto:
        """Retira/desactiva un usuario de la aplicación.

        Parameters
        ----------
        username : str
            Nombre de usuario a retirar

        Returns
        -------
        EjemploRemovedUserResponseDto
            Resultado del retiro del usuario
        """
        # Implementación específica para retiro
        pass

    def _authenticate(self) -> None:
        """Autentica en la aplicación."""
        # Lógica de autenticación específica
        pass

    def _fetch_user_data(self, username: str) -> dict:
        """Obtiene los datos del usuario."""
        # Lógica específica de consulta
        pass
```

#### Métodos Requeridos

Cada módulo debe implementar al menos uno de estos métodos:

1. **`consult_user(username: str)`**: Para consulta de usuarios
2. **`remove_user(username: str)`**: Para retiro de usuarios (opcional)

#### Manejo de Errores

Los módulos utilizan excepciones específicas:

```python
from lib.exceptions import NotFoundError, AutomationError

# Usuario no encontrado
if not user_exists:
    raise NotFoundError(self.i18n('user_not_found'))

# Error en la automatización
if connection_failed:
    raise AutomationError(self.i18n('connection_error'))
```

#### Método de retiro de usuarios

Para la funcionalidad de retiro de usuarios (Bot Retiros), los módulos deben declarar una función `remove_user` así:
```python
# bot/modules/custom_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule

class CustomModule(BaseInjectable, BaseModule):

    def remove_user(self, ...): # Método para retirar usuarios
        ...
```

#### Tipo de retorno

Tanto el método principal `main` como el método de retiro de usuarios `remove_user` deben retornar un objeto
de las clases `ApplicationUserDataResponseDto` y `RemovedUserDataResponseDto` respectivamente. Cada módulo define
una clase personalizada que herede de cada una de las clases mencionadas anteriormente en el archivo que contiene
los [DTOs de automatización](/bot/dtos/automation_dto.py), por ejemplo:
```python
# bot/dtos/automation_dto.py

class CustomModuleResponseDto(ApplicationUserDataResponseDto):

    # Ejemplo de datos que extraerá el método principal del módulo (main)
    active: bool
    locked: bool
    ...


class CustomModuleRemovedUserResponseDto(RemovedUserDataResponseDto):

    # Ejemplo de datos que extraerá el módulo al retirar un usuario (remove_user)
    active: bool
    locked: bool
    message: str
    warning: bool = False
    ...
```
```python
# bot/modules/custom_module.py

from dtos.automation_dto import CustomModuleResponseDto, CustomModuleRemovedUserResponseDto

class CustomModule(BaseInjectable, BaseModule):

    def main(self, ...):
        ...

        return CustomModuleResponseDto(active=True, locked=False, ...)

    def remove_user(self, ...):
        ...

        return CustomModuleRemovedUserResponseDto(
            active=False,
            locked=True,
            message='Usuario retirado correctamente.',
            ...)
```

## Guías de Desarrollo

### Agregar un Nuevo Módulo de Automatización

Para agregar soporte para una nueva aplicación, siga estos pasos:

#### 1. Crear el Módulo

```python
# bot/modules/nueva_app_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule
from lib.exceptions import NotFoundError, AutomationError
from lib.http.sessions import TLSSession
from dtos.automation_dto import NuevaAppResponseDto

class NuevaAppModule(BaseInjectable, BaseModule):
    """Módulo para consultar Nueva App."""

    def __init__(self, i18n: I18N) -> None:
        super().__init__()
        self.i18n = i18n
        self.session = TLSSession()

    def consult_user(self, username: str) -> NuevaAppResponseDto:
        """Consulta un usuario en Nueva App."""
        try:
            self._authenticate()
            user_data = self._get_user_data(username)
            return self._create_response_dto(user_data)
        finally:
            self.close_session()

    def _authenticate(self) -> None:
        """Implementar lógica de autenticación específica."""
        pass

    def _get_user_data(self, username: str) -> dict:
        """Implementar lógica de consulta específica."""
        pass
```

#### 2. Crear el DTO de Respuesta

```python
# bot/dtos/automation_dto.py

class NuevaAppResponseDto(ApplicationUserDataResponseDto):
    """DTO para respuesta de Nueva App."""
    active: bool
    roles: list[str] = []
    last_login: str | None = None
```

#### 3. Registrar el Módulo

```python
# bot/modules/__init__.py
from .nueva_app_module import NuevaAppModule

modules = [
    # ... módulos existentes
    NuevaAppModule,
]
```

#### 4. Configurar en el Servicio de Automatización

```python
# bot/services/automation_service.py

class AutomationService(BaseInjectable, BaseService):
    def __init__(self,
                 # ... otros módulos
                 nueva_app_module: NuevaAppModule,
                 i18n: I18N) -> None:

        # Para consultas
        self._consult_user_modules = [
            # ... módulos existentes
            nueva_app_module,
        ]

        # Para retiros (si aplica)
        self._remove_user_modules = [
            # ... módulos existentes
            nueva_app_module,  # Solo si implementa remove_user()
        ]
```

#### 5. Agregar Configuración

```python
# .env.example
# Nueva App settings
NUEVA_APP_USER=usuario_nueva_app
NUEVA_APP_PASSWORD=contraseña_nueva_app
```

#### 6. Agregar Traducciones

```python
# bot/i18n/es_CO/__init__.py
es_CO = {
    #region applications
    'NuevaAppModule': 'Nueva App',
    #endregion

    #region common
    'active': 'Activo',
    'roles': 'Roles',
    'last_login': 'Último Acceso',
    #endregion
}
```

### Crear un Nuevo Endpoint

#### 1. Crear el Controlador

```python
# bot/controllers/nuevo_controller.py

from lib.base_controller import BaseController, get, post
from lib.auth import auth, AuthRole
from lib.responses import DtoResponse, ErrorResponse
from dtos.nuevo_dto import NuevoRequestDto, NuevoResponseDto

class NuevoController(BaseController):

    @auth(AuthRole.CONSULT)
    @post('/nuevo/crear')
    async def crear(self,
                   data: NuevoRequestDto,
                   service: NuevoService) -> DtoResponse[NuevoResponseDto] | ErrorResponse:
        """Crear nuevo elemento."""
        try:
            resultado = await service.crear(data)
            return DtoResponse[NuevoResponseDto](resultado)
        except Exception as e:
            return ErrorResponse({'error': str(e)})
```

#### 2. Registrar el Controlador

```python
# bot/controllers/__init__.py
from .nuevo_controller import NuevoController

controllers = [
    # ... controladores existentes
    NuevoController,
]
```

### Configurar Variables de Entorno

#### Estructura Recomendada

```ini
# .env

# Configuración de aplicación específica
NUEVA_APP_ENABLED=true
NUEVA_APP_URL=https://nueva-app.empresa.com
NUEVA_APP_USER=usuario_consulta
NUEVA_APP_PASSWORD=contraseña_consulta
NUEVA_APP_TIMEOUT=30

# Para Bot Retiros (si aplica)
NUEVA_APP_RETIROS_USER=usuario_retiros
NUEVA_APP_RETIROS_PASSWORD=contraseña_retiros
```

#### Leer en Configuración

```python
# bot/lib/config.py

# Nueva App settings
NUEVA_APP_ENABLED = _config('NUEVA_APP_ENABLED', cast=bool, default=False)
NUEVA_APP_URL = _config('NUEVA_APP_URL', cast=str, default='')
NUEVA_APP_USER = _config('NUEVA_APP_USER', cast=Secret)
NUEVA_APP_PASSWORD = _config('NUEVA_APP_PASSWORD', cast=Secret)
```

## Testing y Debugging

### Ejecutar Tests

```bash
# Ejecutar tests unitarios
cd bot/
python -m pytest tests/ -v

# Ejecutar tests con cobertura
python -m pytest tests/ --cov=. --cov-report=html

# Ejecutar tests específicos
python -m pytest tests/test_modules.py::test_salud_web_module -v
```

### Debugging de Módulos

#### 1. Usar el Script de Prueba

```python
# bot/test.py - Script para probar módulos individualmente

from modules.salud_web_module import SaludWebModule
from i18n import I18N

async def test_module():
    i18n = I18N()
    module = SaludWebModule(i18n)

    try:
        result = module.consult_user("usuario_prueba")
        print(f"Resultado: {result}")
    except Exception as e:
        print(f"Error: {e}")

# Ejecutar: python test.py
```

#### 2. Logs Detallados

```python
# Configurar logging detallado
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
logger.debug("Mensaje de debug")
```

#### 3. Usar el Cliente REST

```http
### bot/api.http - Cliente REST para VS Code

# Consultar usuario
POST http://localhost:8010/automation/consult
Content-Type: application/json
Authorization: Bearer {{token}}

{
    "usernames": ["usuario_prueba"],
    "applications": ["SaludWebModule"]
}
```

### Mejores Prácticas

#### Desarrollo de Módulos

1. **Manejo de Errores**:
```python
try:
    # Lógica del módulo
    pass
except requests.RequestException as e:
    raise AutomationError(f"Error de conexión: {e}")
except Exception as e:
    raise AutomationError(f"Error inesperado: {e}")
finally:
    self.close_session()
```

2. **Timeouts y Reintentos**:
```python
from lib.tools import retry

@retry(times=3, raise_exceptions=(NotFoundError,))
def consult_user(self, username: str):
    # Lógica con reintentos automáticos
    pass
```

3. **Validación de Datos**:
```python
def _validate_username(self, username: str) -> None:
    if not username or len(username) < 3:
        raise ValueError("Username inválido")
```

#### Seguridad

1. **Credenciales**:
```python
# ✅ Correcto - usar variables de entorno
password = str(config.APP_PASSWORD)

# ❌ Incorrecto - hardcodear credenciales
password = "mi_password_secreto"
```

2. **Logs Seguros**:
```python
# ✅ Correcto - no loggear información sensible
logger.info(f"Consultando usuario: {username}")

# ❌ Incorrecto - loggear credenciales
logger.info(f"Login con password: {password}")
```

#### Performance

1. **Sesiones Reutilizables**:
```python
class MiModulo(BaseModule):
    def __init__(self):
        self.session = TLSSession()  # Reutilizar sesión

    def close_session(self):
        if self.session:
            self.session.close()
```

2. **Async/Await**:
```python
# Para operaciones I/O intensivas
async def consult_multiple_users(self, usernames: list[str]):
    tasks = [self.consult_user(username) for username in usernames]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

### Traducción con I18N

I18N es un módulo de traducción. Se inyecta como dependencia usando el método "Transient" explicado en la sección de [inyección de dependencias](#inyección-de-dependencias).

Cuando se recibe una petición con el header "Accept-Language", los controladores capturan el valor del header y lo asignan al campo "language" del módulo I18N. Esta captura se realiza en el método de configuraciones previas de la librería [BaseController](/bot/lib/base_controller.py) como se muestra a continuación:

```python
# bot/lib/base_controller.py

class BaseController:

    def prev(self) -> None:
        cast(I18N, self.injectable.get(I18N)).language = self.request.headers.get('Accept-Language')
```

De esa forma **I18N** reconoce en qué lenguaje mostrar los mensajes. En caso de no recibir este header, se usará el lenguaje por defecto (es-CO).

NOTA: Su uso no es obligatorio, excepto en los validadores.

#### Uso

Su modo de uso es el siguiente:

1. Agregue el mensaje a cada diccionario de I18N:

```python
# bot/i18n/es_CO/__init__.py

# Diccionario del lenguaje español de Colombia
es_CO = {

    ...,
    '<mensaje de I18N>': 'Texto a mostrar',
}
```

2. Inyectar I18N:

```python
class CustomModule(BaseInjectable):

    def __init__(self,
        ...,
        i18n: I18N # <--- Inyección del I18N
    ):
        super().__init__()
        self.i18n = i18n # <--- Asignarlo como atributo de la clase
```

3. Ejecutar I18N pasándole el mensaje a mostrar:

```python
class CustomModule(BaseInjectable):

    def main(self, ...):

        response = requests.get('www.example.com')
        if not response.ok:
            raise AutomationError(
                self.i18n('<mensaje de I18N>') # Clave del mensaje de I18N
            )
```

#### Agregar nuevo lenguaje

Para agregar un nuevo lenguaje, siga los siguientes pasos:

1. Cree una carpeta nombrada con el código del lenguaje en la carpeta del módulo I18N (bot/i18n). Dentro de la carpeta cree un archivo llamado "**\_\_init\_\_.py**"":

NOTA: Al crear la carpeta, no utilice guiones medios ni espacios, sólo letras y guiones bajos.

![Lenguaje I18N](/docs/screenshots/development_i18n_language.png)

2. Cree un diccionario de Python con el código del lenguaje dentro del **\_\_init\_\_.py** e ingrese los mensajes, así:

```python
# bot/i18n/es_US/__init__.py

en_US = {
    '<mensaje i18n 1>': 'Texto a mostrar 1',
    '<mensaje i18n 2>': 'Texto a mostrar 2',
    ...
}
```

También puede usar regiones para clasificar y/o colapsar los mensajes (normalmente, se crea una región llamada "common" que ubica los mensajes más comunes que aplican para cualquier módulo):

```python
# bot/i18n/es_US/__init__.py

en_US = {
    #region region1
    '<mensaje i18n 1>': 'Texto a mostrar 1',
    '<mensaje i18n 2>': 'Texto a mostrar 2',
    #endregion

    #region region2
    '<mensaje i18n 3>': 'Texto a mostrar 3',
    '<mensaje i18n 4>': 'Texto a mostrar 4',
    #endregion
    ...
}
```

3. Importe el nuevo lenguaje en el [módulo I18N](bot/i18n/__init__.py) y objeto al diccionario de lenguajes.

```python
# bot/i18n/__init__.py

from .en_US import en_US # <--- Nuevo lenguaje

class I18N(BaseInjectable):

    languages = {
        ...,
        'en_US': en_US, # <--- Nuevo lenguaje
    }
```

#### Mensajes con parámetros

Es posible pasar parámetros a un mensaje mediante el parámetro "**format_values**".

Suponiendo que tenemos el siguiente mensaje:

```python
# bot/i18n/es_US/__init__.py

en_US = {
    ...,
    '<mensaje i18n>': 'Texto a mostrar con el parámetro "{valor}".',
}
```

Podemos reemplazar `{valor}` por un parámetro de la siguiente manera:

```python
class CustomModule(BaseInjectable):

    def main(self, ...):

        response = requests.get('www.example.com')
        if not response.ok:
            message = 'error'
            raise AutomationError(
                # Mensaje de I18N pasando `message` como parámetro
                # para reemplazar "{valor}"
                self.i18n('<clave I18N>', {'valor': message})
            )
```

De esta forma, el mensaje resultante sería:

```
Texto a mostrar con un parámetro "error".
```

### Librerías

#### Definición
Son módulos para realizar operaciones varias como conexiones a APIs, envío de correos, exporte de datos, conexión a plataformas, entre otras. Se pueden inyectar como se mencionó en la sección de [inyección de dependencias](#inyección-de-dependencias) pero no es obligatoria su inyección, la decisión de inyectar una librería o no depende de la necesidad del desarrollador y que problema quiera resolver. Se ubican en la carpeta "**lib**".

Ejemplo de librería de SAML:
```python
from lib.base_injectable import BaseInjectable

class SAML(BaseInjectable):

    def __init__(self,
        # Se inyectan las dependencias requeridas
        # por ejemplo, la configuración del servidor
        i18n: I18N
    ):
        super().__init__()
        self.i18n = I18N
        self.session = TLSSession()
        ...

    def authenticate(self):
        ...
```

#### Descripción de las librerías
| Librería| Descripción|
| ---- | ---- |
| [**active_directory.py**](/bot/lib/active_directory.py) | Dependencia ActiveDirectory para consultar usuarios y hacer login en el directorio activo. |
| [**ariba_session.py**](/bot/lib/ariba_session.py) | Clase **AribaSession** para realizar la autenticación SAML y manejar la sesión en Ariba. |
| [**ariba_retiros_session.py**](/bot/lib/ariba_retiros_session.py) | Clase **AribaRetirosSession** para realizar la autenticación SAML y manejar la sesión del usuario de retiros en Ariba. |
| [**auth.py**](/bot/lib/auth.py) | Decorador **auth** para restringir un endpoint a sólo clientes autenticados y métodos para crear un token de autenticación válido. |
| [**base_controller.py**](/bot/lib/base_controller.py) | Clase base para los controladores y decoradores para marcar especificar el método (GET, POST, DELETE o PUT) y ruta de cada endpoint. |
| [**base_dto.py**](/bot/lib/base_dto.py) | Clase base para los DTOs de petición y de respuesta. |
| [**base_exception.py**](/bot/lib/base_exception.py) | Clase base para excepciones. |
| [**base_export.py**](/bot/lib/base_export.py) | Clase base para módulos de exportación de datos. |
| [**base_injectable.py**](/bot/lib/base_injectable.py) | Clase base para todo lo que se vaya a inyectar como una dependencia. |
| [**base_middleware.py**](/bot/lib/base_middleware.py) | Clase base para middlewares. |
| [**base_model.py**](/bot/lib/base_model.py) | Clase base para modelos. |
| [**base_module.py**](/bot/lib/base_module.py) | Clase base para módulos de automatización. |
| [**base_response.py**](/bot/lib/base_response.py) | Clase base para respuestas. |
| [**base_serializer.py**](/bot/lib/base_serializer.py) | Clase base para serializadores que convierten uno o varios modelos en DTOs. |
| [**base_service.py**](/bot/lib/base_service.py) | Clase base para servicios. |
| [**base_validator.py**](/bot/lib/base_validator.py) | Clase base para validadores. |
| [**config.py**](/bot/lib/config.py) | Clase **Config** para almacenar y administrar la configuración del servidor. |
| [**data_frame_tools.py**](/bot/lib/data_frame_tools.py) | Clase **DataFrame** para crear y manejar marcos de datos a partir de archivos CSV o Excel. |
| [**env.py**](/bot/lib/env.py) | Clase **Env** para la lectura y almacenamiento de las variables de entorno del archivo **.env**. |
| [**exceptions.py**](/bot/lib/exceptions.py) | Excepciones personalizadas. |
| [**export.py**](/bot/lib/export.py) | Clase **ExportData** para exportar DTOs. |
| [**filter.py**](/bot/lib/filter.py) | Clase **Filter** para crear filtros de consultas de búsqueda. |
| [**injectable.py**](/bot/lib/injectable.py) | Lógica de la inyección de dependencias. |
| [**injectables.py**](/bot/lib/injectables.py) | Módulo de inyección de dependencias. |
| [**interfaces.py**](/bot/lib/interfaces.py) | Interfaces. |
| [**log.py**](/bot/lib/log.py) | Clase **Log** para crear logs. |
| [**logger.py**](/bot/lib/logger.py) | Logger del proyecto. |
| [**mail_template.py**](/bot/lib/mail_template.py) | Plantillas para el cuerpo de los correos. |
| [**mail.py**](/bot/lib/mail.py) | Clase **Mail** para enviar correos. |
| [**msal.py**](/bot/lib/msal.py) | Clase **MSAL** para realizar consultas mediante una API de Azure. |
| [**pagination.py**](/bot/lib/pagination.py) | Clase **Pagination** para crear un objeto con datos paginados. |
| [**porfin_session.py**](/bot/lib/porfin_session.py) | Clase **PorfinSession** para realizar la autenticación y manejar la sesión en Porfin. |
| [**porfin_retiros_session.py**](/bot/lib/porfin_retiros_session.py) | Clase **PorfinRetirosSession** para realizar la autenticación y manejar la sesión del usuario de retiros en Porfin. |
| [**query.py**](/bot/lib/query.py) | Clase **Query** para crear consultas de Mongo complejas que incorporen proyección y traducción de datos en Mongo. |
| [**responses.py**](/bot/lib/responses.py) | Respuestas personalizadas. |
| [**results_exporter.py**](/bot/lib/results_exporter.py) | Clase **ResultsExporter** para exportar instancias de los modelo **ConsultedUser** o **RemovedUser**, es decir, los resultados de la consulta o retiro de uno o muchos usuarios. |
| [**routes.py**](/bot/lib/routes.py) | Implementación personalizada de la clase **Route** de **Starlette** para la incluir la inyección de dependencias. |
| [**salesforce.py**](/bot/lib/salesforce.py) | Clase **Salesforce** para crear conexiones a la API de Salesforce. |
| [**salud_web_session.py**](/bot/lib/salud_web_session.py) | Clase **SaludWebSession** para realizar la autenticación y manejar la sesión en Salud Web. |
| [**salud_web_session_retiros.py**](/bot/lib/salud_web_session_retiros.py) | Clase **SaludWebRetirosSession** para realizar la autenticación y manejar la sesión del usuario de retiros en Salud Web. |
| [**saml.py**](/bot/lib/saml.py) | Clase **SAML** para autenticar mediante SAML. |
| [**tools.py**](/bot/lib/tools.py) | Herramientas (funciones varias). |
| [**translate.py**](/bot/lib/translate.py) | Clase **Translate** para la traducción de datos en Mongo. |
| [**user_session.py**](/bot/lib/user_session.py) | Backend del middleware de autenticación. |

### Librería `http`

Esta librería proporciona [**adaptadores HTTP**](/bot/lib/http/adapters.py), [**políticas de cookies**](/bot/lib/http/cookie_policies.py) y [**sesiones personalizadas**](/bot/lib/http/sessions.py). Entre las sesiones, se encuentra la clase **TLSSession** para crear una sesión que registra el adaptador HTTP personalizado **TLSAdapter** el cual admite conexiones SSL que hacen uso de una versión de OpenSSL antigua. También implementa la clase **AllowsEmptyDomainsPolicy** para procesar cookies cuyo  *domain* es un string vacío.

```python
class TLSSession(requests.Session):
    """A `requests.Session` object that
    accepts unsafe and insecure connections
    and allows empty-domain cookies.
    """

    timeout: int
    """Requests timeout."""

    def __init__(self, timeout: int = 10) -> None:
        """Creates a `requests.Session` object registering
        the custom `TLSAdapter` HTTP adapter.

        It also implements the `AllowsEmptyDomainsPolicy`
        policy to allow empty-domain cookies.

        Parameters
        ----------
        timeout : int | None, optional
            Requests timeout in seconds, by default None.
        """

        super().__init__()
        self.verify = False
        self.timeout = timeout
        self.mount('https://', TLSAdapter())
        self.cookies.set_policy(AllowsEmptyDomainsPolicy())

    def request(self, method: str | bytes, url: str | bytes, **kwargs) -> requests.Response:
        kwargs.setdefault('timeout', self.timeout)
        return super().request(method, url, **kwargs)
```

### Librería `pdf`

Esta librería proporciona una [clase](/bot/lib/pdf/pdf.py) para construir archivos PDF utilizando la biblioteca [`reportlab`](https://docs.reportlab.com/demos/hello_world/hello_world/). La librería [**results_exporter.py**](/bot/lib/results_exporter.py) utiliza esta clase para generar el informe correspondiente a la exportación de usuarios consultados y retirados.

#### Ejemplo de uso

La clase [`PDF`](/bot/lib/pdf/pdf.py) contiene métodos para agregar diferentes elementos al PDF y renderizarlos en un archivo. Por ejemplo, el siguiente código genera un PDF con el texto **¡Hola Mundo!**:
```python
from lib.pdf import PDF
from lib.pdf.interfaces import *

# Crear objeto de PDF
pdf = PDF()

# Agregar párrafo con el texto "¡Hola Mundo!"
pdf.add_paragraph('¡Hola Mundo!')

# Renderizar elementos del PDF y obtener el contenido en bytes
content = pdf.build(title='Mi primer PDF')

# Guardar contenido a un archivo .pdf
with open('my_pdf.pdf', 'wb') as f:
    f.write(content)
```

El PDF resultante luciría así:

![Hola Mundo en PDF](/docs/screenshots/development_pdf_hello_world.png)

La clase `PDF` hereda de **BaseInjectable**, por lo tanto, también puede ser inyectada como una dependencia tal y como se explica [aquí](#inyección-de-dependencias):
```python
from lib.pdf import PDF
from lib.pdf.interfaces import *

class CustomModule(BaseInjectable, BaseModule):

    def __init__(self,
        pdf: PDF # <--- Inyección del módulo PDF
    ) -> None:
        super().__init__()
        self.pdf = pdf

    # Método para crear el PDF
    def build_pdf(self):

        # Agregar párrafo con el texto "¡Hola Mundo!"
        self.pdf.add_paragraph('¡Hola Mundo!')

        # Renderizar elementos del PDF y obtener el contenido en bytes
        content = self.pdf.build(title='Mi primer PDF')

        # Guardar contenido a un archivo .pdf
        with open('my_pdf.pdf', 'wb') as f:
            f.write(content)
```

#### Métodos

A continuación se presenta la descripción de cada método que contiene el módulo para generar el PDF y algunos métodos de utilidad:

| Método | Descripción |
| ---- | ---- |
| **add_front_page** | Agrega una portada con un formato ya preestablecido. Para ver una ejemplo de portada [haga click aquí](/docs/screenshots/development_pdf_front_page.png). |
| **add_line** | Agrega una línea o separador |
| **add_page_break** | Agrega un salto de página. |
| **add_paragraph** | Agrega un párrafo. |
| **add_spacer** | Agrega un espaciado. |
| **add_subtitle** | Agrega un subtítulo. |
| **add_table** | Agrega una tabla. |
| **add_table_from_dict** | Agrega una tabla a partir de un diccionario. |
| **add_table_from_list** | Agrega una tabla a partir de una lista. |
| **add_table_of_contents** | Agrega una tabla de contenidos. Para ver un ejemplo de tabla de contenidos [haga click aquí](/docs/screenshots/development_pdf_toc.png). |
| **add_title** | Agrega un título. |
| **add_title1** | Agrega un título de primer nivel. |
| **add_title2** | Agrega un título de segundo nivel. |
| **add_title3** | Agrega un título de tercer nivel. |
| **build** | Renderiza todos los elementos y retorna el contenido del PDF en bytes. |
| **get_color_from_hex** | Obtiene una instancia de la clase [`Color`](https://docs.reportlab.com/reportlab/userguide/ch2_graphics/#colors) de **reportlab** a partir de un color hexadecimal. |
| **get_color_from_rgb** | Obtiene una instancia de la clase [`Color`](https://docs.reportlab.com/reportlab/userguide/ch2_graphics/#colors) de **reportlab** a partir de un color RGB. |

#### PDF de ejemplo

Si está viendo esta documentación desde Visual Studio Code, puede instalar la extensión [vscode-pdf](https://marketplace.visualstudio.com/items?itemName=tomoki1207.pdf) y [hacer click aquí](/docs/files/example.pdf) para abrir el PDF de ejemplo que contiene todos los elementos mencionados anteriormente (portada, tabla de contenido, separadores, saltos de página, párrafos, espaciados, títulos y subtítulos).

También puede abrir manualmente el archivo de la ruta **/docs/files/example.pdf** y verlo desde algún visor de PDF o un navegador web.

### Librería `validator`

Esta librería proporciona métodos de validación para campos de DTOs.

#### Ejemplo de uso

A continuación se presenta un ejemplo de uso en un validador de un modelo de roles:

```python
# bot/validators/role_validator.py

from i18n import I18N
from lib.base_injectable import BaseInjectable
from lib.base_validator import BaseValidator
from lib.validator.data_validator import DataValidator
from lib.validator.validator import Validator

class RoleValidator(BaseInjectable, BaseValidator):

    def __init__(self, i18n: I18N) -> None:
        super().__init__()
        self.i18n = i18n

    # Valida que el nombre del rol que se va a crear no exista
    async def validate_name(self, name: str) -> bool:
        role = await self.service.filter_name(name)
        return True if role is None else False

    # Crear validador de datos
    def validator(self, role: RoleRequestDto) -> DataValidator:
        validator = DataValidator()

        # Agregar una validación para un campo
        validator.add(
            # Nombre de la validación
            # Se recomienda usar el mismo nombre del campo a validar
            'name',

            # Tipo de campo "str", el campo es "name"
            Validator[str](role.name)
                .required( # El campo es requerido
                    'role_is_required') # Mensaje I18N personalizado (OPCIONAL)
                .custom( # Validación personalizada
                    self.validate_name, # Función de la validación personalizada
                    # Mensaje I18N personalizado (OPCIONAL)
                    message='role_already_exists',
                    # Parámetros del mensaje I18N personalizado (OPCIONAL)
                    format_values={'name': role.name}))

        return validator

    # Ejecutar el validador
    async def validate(self, role: RoleCreateDto) -> bool:
        validator = self.validator(role)
        if await validator.validate():
            return True

        # Obtener mensajes de error en caso de que hayan campos no válidos
        self.error = validator.get_error(self.i18n)
        return False
```

Como se puede observar en el ejemplo anterior, los validadores tienen un mensaje predefinido que se puede sobrescribir mediante el parámetro "**message**". En el caso del validador "**custom**", este contiene un parámetro "**format_values**" que permite pasar parámetros al mensaje personalizado tal y como se explica en la sección de [mensajes con parámetros de I18N](#mensajes-con-parámetros).

#### Descripción de los validadores

Los validadores definen qué tipo de validación se hará sobre el elemento.

Cada validador tiene su respectiva documentación. Para saber como se usa un validador diríjase a su respectivo archivo y revise el **docstring** tal y como se menciona al [inicio de esta guía](#guía-de-modificaciones-del-bot).

A continuación se presenta la descripción de cada validador:

| Validador | Descripción |
| ---- | ---- |
| [**cell_phone_validator.py**](/bot/lib/validator/validators/cell_phone_validator.py) | Validador de números de teléfono móvil. |
| [**custom_validator.py**](/bot/lib/validator/validators/custom_validator.py) | Validador personalizado. |
| [**email_validator.py**](/bot/lib/validator/validators/email_validator.py) | Validador de emails. |
| [**greater_equals_than_validator.py**](/bot/lib/validator/validators/greater_equals_than_validator.py) | Mayor o igual que. |
| [**greater_than_validator.py**](/bot/lib/validator/validators/greater_than_validator.py) | Mayor que. |
| [**id_card_validator.py**](/bot/lib/validator/validators/id_card_validator.py) | Validador de números de documento. |
| [**in_validator.py**](/bot/lib/validator/validators/in_validator.py) | Validar si el elemento está en una lista. |
| [**lower_equals_than_validator.py**](/bot/lib/validator/validators/lower_equals_than_validator.py) | Menor o igual que. |
| [**lower_than_validator.py**](/bot/lib/validator/validators/lower_than_validator.py) | Menor que. |
| [**max_length_validator.py**](/bot/lib/validator/validators/max_length_validator.py) | Longitud máxima. |
| [**min_length_validator.py**](/bot/lib/validator/validators/min_length_validator.py) | Longitud mínima. |
| [**object_compare_validator.py**](/bot/lib/validator/validators/object_compare_validator.py) | Comparar similitud entre dos diccionarios. |
| [**password_validator.py**](/bot/lib/validator/validators/password_validator.py) | Validador de contraseña  |
| [**phone_validator.py**](/bot/lib/validator/validators/phone_validator.py) | Validador de número de teléfono fijo  |
| [**range_length_validator.py**](/bot/lib/validator/validators/range_length_validator.py) | Validar que la longitud del elemento esté en un rango. |
| [**range_validator.py**](/bot/lib/validator/validators/range_validator.py) | Validar que el número esté en un rango. |
| [**regular_expression_validator.py**](/bot/lib/validator/validators/regular_expression_validator.py) | Validar que el elemento existe usando regex. |
| [**required_validator.py**](/bot/lib/validator/validators/required_validator.py) | Requerido. |
| [**url_validator.py**](/bot/lib/validator/validators/url_validator.py) | Validador de URL. |

#### Descripción de los operadores

Los operadores permiten controlar el flujo de ejecución de las validaciones.

Cada operador tiene su respectiva documentación. Para saber como se usa un operador diríjase a su respectivo archivo y revise el **docstring** tal y como se menciona al [inicio de esta guía](#guía-de-modificaciones-del-bot).

A continuación se presenta la descripción de cada operador:

| Operador | Descripción |
| ---- | ---- |
| [**if_not_operator.py**](/bot/lib/validator/operators/if_not_operator.py) | Continua con la validación siguiente si la condición no se cumple. |
| [**if_operator.py**](/bot/lib/validator/operators/if_operator.py) | Continua con la validación siguiente si la condición se cumple. |
| [**stop_operator.py**](/bot/lib/validator/operators/stop_operator.py) | Continua con la validación siguiente si hasta el momento todas las validaciones anteriores han sido correctas. |
