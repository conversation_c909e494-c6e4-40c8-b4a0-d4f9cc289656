//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class MaxLengthValidator extends ClassValidator {

  private maxLengthValue: number;

  public constructor(value: unknown, maxLengthValue: number, message?: string) {
    super(value);
    this.maxLengthValue = maxLengthValue;
    this.message = (message) ? message : `${i18n.global.t('valueMustHaveMax', { number: this.maxLengthValue })}`;
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    return this.isValid = (value) ? value.length <= this.maxLengthValue : true;
  }
}

export default MaxLengthValidator;