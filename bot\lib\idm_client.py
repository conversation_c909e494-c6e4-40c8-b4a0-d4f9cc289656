from dataclasses import dataclass
from typing import Any

import httpx
import orjson

from lib import config
from lib.base_injectable import BaseInjectable

HEADERS = {'Content-Type': 'application/json'}


@dataclass
class IDMResponse:
    content: Any
    is_success: bool = False
    status_code: int = 0
    request_data: dict[str, Any] | None = None


class IDMClient(BaseInjectable):
    async def send_removed_user_result(
        self,
        transaction_id: str,
        username: str,
        results: list[dict[str, Any]],
    ) -> IDMResponse:
        payload = None
        try:
            if not config.IDM_API_URL:
                return IDMResponse(
                    content='La URL de la API de IDM es requerida',
                )

            async with httpx.AsyncClient(verify=False) as client:
                payload = self.__build_payload(
                    transaction_id, username, results
                )
                response = await client.post(
                    config.IDM_API_URL,
                    content=orjson.dumps(payload),
                    headers=HEADERS,
                    auth=httpx.BasicAuth(
                        str(config.IDM_API_USER),
                        str(config.IDM_API_CRED),
                    ),
                )
                return IDMResponse(
                    is_success=response.is_success,
                    status_code=response.status_code,
                    content=response.text,
                    request_data=payload,
                )
        except Exception as e:
            return IDMResponse(
                content=f'Ha ocurrido un error al enviar los datos a IDM: {e}',
                request_data=payload,
            )

    def __build_payload(
        self,
        transaction_id: str,
        username: str,
        results: list[dict[str, Any]],
    ) -> dict[str, Any]:
        return {
            'transactionId': transaction_id,
            username: [
                {
                    'application': result['application'],
                    'data': result['data'],
                    'error': result['error'],
                    'userNotFound': result['userNotFound'],
                    'ignored': result['ignored'],
                    'ignoreMessage': result['warning'],
                } for result in results
            ]
        }
